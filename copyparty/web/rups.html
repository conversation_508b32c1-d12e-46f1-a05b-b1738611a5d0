<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<title>{{ s_doctitle }}</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=0.8">
    <meta name="robots" content="noindex, nofollow">
	<meta name="theme-color" content="#{{ tcolor }}">
	<link rel="stylesheet" media="screen" href="{{ r }}/.cpr/rups.css?_={{ ts }}">
	<link rel="stylesheet" media="screen" href="{{ r }}/.cpr/ui.css?_={{ ts }}">
{{ html_head }}
</head>

<body>
	<div id="wrap">
        <a href="#" id="re">refresh</a>
        <a href="{{ r }}/?h">control-panel</a>
        &nbsp; Filter: <input type="text" id="filter" size="20" placeholder="documents/passwords" />
        &nbsp; <span id="hits"></span>
        <table id="tab"><thead><tr>
            <th>size</th>
            <th>who</th>
            <th>when</th>
            <th>age</th>
            <th>dir</th>
            <th>file</th>
        </tr></thead><tbody id="tb"></tbody></table>
    </div>
	<a href="#" id="repl">π</a>
	<script>

var SR="{{ r }}",
	lang="{{ lang }}",
    dutc={{ this.args.js_utc }},
	dfavico="{{ favico }}";

var STG = window.localStorage;
document.documentElement.className = (STG && STG.cpp_thm) || "{{ this.args.theme }}";

</script>
<script src="{{ r }}/.cpr/util.js?_={{ ts }}"></script>
<script>var V={{ v }};</script>
<script src="{{ r }}/.cpr/rups.js?_={{ ts }}"></script>
{%- if js %}
<script src="{{ js }}_={{ ts }}"></script>
{%- endif %}
</body>
</html>

