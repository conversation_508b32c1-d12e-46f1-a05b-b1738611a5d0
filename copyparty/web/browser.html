<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<title>{{ title }}</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=0.8, minimum-scale=0.6">
	<meta name="theme-color" content="#{{ tcolor }}">
	<link rel="stylesheet" media="screen" href="{{ r }}/.cpr/ui.css?_={{ ts }}">
	<link rel="stylesheet" media="screen" href="{{ r }}/.cpr/browser.css?_={{ ts }}">
{{ html_head }}
	{%- if css %}
	<link rel="stylesheet" media="screen" href="{{ css }}_={{ ts }}">
	{%- endif %}
</head>

<body>
	<div id="ops"></div>

	<div id="op_search" class="opview">
		{%- if have_tags_idx %}
		<div id="srch_form" class="tags opbox"></div>
		{%- else %}
		<div id="srch_form" class="opbox"></div>
		{%- endif %}
		<div id="srch_q"></div>
	</div>

	<div id="op_player" class="opview opbox opwide"></div>

	<div id="op_bup" class="opview opbox {% if not ls0 %}act{% endif %}">
		<div id="u2err"></div>
		<form method="post" enctype="multipart/form-data" accept-charset="utf-8" action="{{ url_suf }}">
			<input type="hidden" name="act" value="bput" />
			<input type="file" name="f" multiple /><br />
			<input type="submit" value="start upload">
		</form>
		<a id="bbsw" href="?b=u" rel="nofollow"><br />switch to basic browser</a>
	</div>

	<div id="op_mkdir" class="opview opbox {% if not ls0 %}act{% endif %}">
		<form method="post" enctype="multipart/form-data" accept-charset="utf-8" action="{{ url_suf }}">
			<input type="hidden" name="act" value="mkdir" />
			📂<input type="text" name="name" class="i" placeholder="awesome mix vol.1">
			<input type="submit" value="make directory">
		</form>
	</div>

	<div id="op_new_md" class="opview opbox">
		<form method="post" enctype="multipart/form-data" accept-charset="utf-8" action="{{ url_suf }}">
			<input type="hidden" name="act" value="new_md" />
			📝<input type="text" name="name" class="i" placeholder="weekend-plans">
			<input type="submit" value="new markdown doc">
		</form>
	</div>

	<div id="op_msg" class="opview opbox {% if not ls0 %}act{% endif %}">
		<form method="post" enctype="application/x-www-form-urlencoded" accept-charset="utf-8" action="{{ url_suf }}">
			📟<input type="text" name="msg" class="i" placeholder="lorem ipsum dolor sit amet">
			<input type="submit" value="send msg to srv log">
		</form>
	</div>

	<div id="op_unpost" class="opview opbox"></div>

	<div id="op_up2k" class="opview"></div>

	<div id="op_cfg" class="opview opbox opwide"></div>

	<h1 id="path">
		<a href="#" id="entree">🌲</a>
		{%- for n in vpnodes %}
		<a href="{{ r }}/{{ n[0] }}">{{ n[1] }}</a>
		{%- endfor %}
	</h1>

	<div id="tree"></div>

<div id="wrap">

	{%- if doc %}
	<div id="bdoc"><pre>{{ doc|e }}</pre></div>
	{%- else %}
	<div id="bdoc"></div>
	{%- endif %}

	<div id="pro" class="logue">{{ "" if sb_lg else logues[0] }}</div>

	<table id="files">
		<thead>
			<tr>
				<th name="lead"><span>c</span></th>
				<th name="href"><span>File Name</span></th>
				<th name="sz" sort="int"><span>Size</span></th>
				{%- for k in taglist %}
					{%- if k.startswith('.') %}
				<th name="tags/{{ k }}" sort="int"><span>{{ k[1:] }}</span></th>
					{%- else %}
				<th name="tags/{{ k }}"><span>{{ k[0]|upper }}{{ k[1:] }}</span></th>
					{%- endif %}
				{%- endfor %}
				<th name="ext"><span>T</span></th>
				<th name="ts"><span>Date</span></th>
			</tr>
		</thead>
<tbody>

{%- for f in files %}
<tr><td>{{ f.lead }}</td><td><a href="{{ f.href }}">{{ f.name|e }}</a></td><td>{{ f.sz }}</td>
{%- if f.tags is defined %}
	{%- for k in taglist %}<td>{{ f.tags[k]|e }}</td>{%- endfor %}
{%- endif %}<td>{{ f.ext }}</td><td>{{ f.dt }}</td></tr>
{%- endfor %}

		</tbody>
	</table>

	<div id="epi" class="logue">{{ "" if sb_lg else logues[1] }}</div>

	<h2 id="wfp"><a href="{{ r }}/?h" id="goh">control-panel</a></h2>

	<a href="#" id="repl">π</a>

</div>

	<div id="srv_info"><span>{{ srv_info }}</span></div>

	<div id="widget"></div>

	<script>
		var SR = "{{ r }}",
			CGV1 = {{ cgv1 }},
			CGV = {{ cgv|tojson }},
			TS = "{{ ts }}",
			dtheme = "{{ dtheme }}",
			srvinf = "{{ srv_info }}",
			lang = "{{ lang }}",
			dfavico = "{{ favico }}",
			have_tags_idx = {{ have_tags_idx }},
			sb_lg = "{{ sb_lg }}",
			logues = {{ logues|tojson if sb_lg else "[]" }},
			ls0 = {{ ls0|tojson }};

		var STG = window.localStorage;
		document.documentElement.className = (STG && STG.cpp_thm) || dtheme;
	</script>
	<script src="{{ r }}/.cpr/util.js?_={{ ts }}"></script>
	<script src="{{ r }}/.cpr/baguettebox.js?_={{ ts }}"></script>
	<script src="{{ r }}/.cpr/browser.js?_={{ ts }}"></script>
	<script src="{{ r }}/.cpr/up2k.js?_={{ ts }}"></script>
	{%- if js %}
	<script src="{{ js }}_={{ ts }}"></script>
	{%- endif %}
</body>

</html>

