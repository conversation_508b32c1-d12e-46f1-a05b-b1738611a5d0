<!DOCTYPE html><html><head>
	<meta charset="utf-8">
	<title>📝 {{ title }}</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=0.7">
	<meta name="theme-color" content="#{{ tcolor }}">
	<link rel="stylesheet" href="{{ r }}/.cpr/ui.css?_={{ ts }}">
	<link rel="stylesheet" href="{{ r }}/.cpr/md.css?_={{ ts }}">
	{%- if edit %}
	<link rel="stylesheet" href="{{ r }}/.cpr/md2.css?_={{ ts }}">
	{%- endif %}
{{ html_head }}
</head>
<body>
	<div id="mn"></div>
	<div id="mh">
		<a id="lightswitch" href="#">go dark</a>
		<a id="navtoggle" href="#">hide nav</a>
		{%- if edit %}
			<a id="save" href="{{ arg_base }}edit" tt="Hotkey: ctrl-s">save</a>
			<a id="sbs" href="#" tt="editor and preview side by side">sbs</a>
			<a id="nsbs" href="#" tt="switch between editor and preview$NHotkey: ctrl-e">editor</a>
			<div id="toolsbox">
				<a id="tools" href="#">tools</a>
				<a id="fmt_table" href="#">prettify table (ctrl-k)</a>
				<a id="iter_uni" href="#">non-ascii: iterate (ctrl-u)</a>
				<a id="mark_uni" href="#">non-ascii: markup</a>
				<a id="cfg_uni" href="#">non-ascii: whitelist</a>
				<a id="help" href="#">help</a>
			</div>
			<span id="lno">L#</span>
		{%- else %}
			<a href="{{ arg_base }}edit" tt="good: higher performance$Ngood: same document width as viewer$Nbad: assumes you know markdown">edit (basic)</a>
			<a href="{{ arg_base }}edit2" id="edit2" tt="not in-house so probably less buggy">edit (fancy)</a>
			<a href="{{ arg_base }}">view raw</a>
		{%- endif %}
	</div>
	<div id="toc"></div>
	<div id="mtw">
		<textarea id="mt" autocomplete="off">{{ md }}</textarea>
	</div>
	<div id="mw">
		<div id="ml">
			<div style="text-align:center;margin:5em 0">
				<div style="font-size:2em;margin:1em 0">Loading</div>
				if you're still reading this, check that javascript is allowed
			</div>
		</div>
		<div id="mp" class="mdo"></div>
	</div>
	<a href="#" id="repl">π</a>

	{%- if edit %}
	<div id="helpbox">
		<textarea autocomplete="off">

write markdown (most html is 🙆 too)

## hotkey list
* `Ctrl-S` to save
* `Ctrl-E` to toggle mode
* `Ctrl-K` to prettyprint a table
* `Ctrl-U` to iterate non-ascii chars
* `Ctrl-H` / `Ctrl-Shift-H` to create a header
* `TAB` / `Shift-TAB` to indent/dedent a selection

## toolbar
1. toggle dark mode
2. show/hide navigation bar
3. save changes on server
4. side-by-side editing
5. toggle editor/preview
6. this thing :^)

## markdown
|||
|--|--|
|`**bold**`|**bold**|
|`_italic_`|_italic_|
|`~~strike~~`|~~strike~~|
|`` `code` ``|`code`|
|`[](#hotkey-list)`|[](#hotkey-list)|
|`[](/foo/bar.md#header)`|[](/foo/bar.md#header)|
|`<blink>💯</blink>`|<blink>💯</blink>|

## tables
    |left-aligned|centered|right-aligned
    | ---------- | :----: | ----------:
    |one         |two     |three

|left-aligned|centered|right-aligned
| ---------- | :----: | ----------:
|one         |two     |three

## lists
	* one
	* two
	1. one
	1. two
* one
* two
1. one
1. two

## headers
	# level 1
	## level 2
	### level 3

## quote
	> hello
> hello

## codeblock
		four spaces (no tab pls)

## code in lists
	* foo
	  bar
          six spaces total
* foo
  bar
      six spaces total
.
		</textarea>
	</div>
	{%- endif %}

	<script>

var SR = "{{ r }}",
	last_modified = {{ lastmod }},
	have_emp = {{ "true" if have_emp else "false" }},
	dfavico = "{{ favico }}";

var md_opt = {
	link_md_as_html: false,
	modpoll_freq: {{ md_chk_rate }}
};

(function () {
	var l = window.localStorage,
		drk = (l && l.light) != 1,
		btn = document.getElementById("lightswitch"),
		f = function (e) {
if (e) { e.preventDefault(); drk = !drk; }
document.documentElement.className = drk? "z":"y";
btn.innerHTML = "go " + (drk ? "light":"dark");
try { l.light = drk? 0:1; } catch (ex) { }
		};
	btn.onclick = f;
	f();
})();

	</script>
	<script src="{{ r }}/.cpr/util.js?_={{ ts }}"></script>
	<script src="{{ r }}/.cpr/deps/marked.js?_={{ ts }}"></script>
	<script src="{{ r }}/.cpr/md.js?_={{ ts }}"></script>
	{%- if edit %}
	<script src="{{ r }}/.cpr/md2.js?_={{ ts }}"></script>
	{%- endif %}
	{%- if js %}
	<script src="{{ js }}_={{ ts }}"></script>
	{%- endif %}
</body></html>

