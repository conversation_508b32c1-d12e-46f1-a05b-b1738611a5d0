html .editor-toolbar>button { margin-left: -1px; border: 1px solid rgba(0,0,0,0.1) }
html .editor-toolbar>button+button { border-left: 1px solid rgba(0,0,0,0) }
html .editor-toolbar>button:hover,
html .editor-toolbar>button:active { box-shadow: 0 .1em .3em #999; z-index: 9 }
html .editor-toolbar>button:active,
html .editor-toolbar>button.active { border-color: rgba(0,0,0,0.4); background: #fc0 }
html .editor-toolbar>i.separator { border-left: 1px solid #ccc; }
html .editor-toolbar.disabled-for-preview>button:not(.no-disable) { opacity: .35 }



html {
	line-height: 1.5em;
}
html, body {
	margin: 0;
	padding: 0;
	min-height: 100%;
	font-family: sans-serif;
	font-family: var(--font-main), sans-serif;
	background: #f7f7f7;
	color: #333;
}
#toast {
	bottom: auto;
	top: 1.4em;
}
#repl {
	position: absolute;
	top: 0;
	right: .5em;
	border: none;
	color: inherit;
	background: none;
	text-decoration: none;
}



#mn {
	font-weight: normal;
	margin: 1.3em 0 .7em 1em;
}
#mn a {
	color: #444;
	margin: 0 0 0 -.2em;
	padding: 0 0 0 .4em;
	text-decoration: none;
	/* ie: */
	border-bottom: .1em solid #777\9;
	margin-right: 1em\9;
}
#mn a:first-child {
	padding-left: .5em;
}
#mn a:last-child {
	padding-right: .5em;
}
#mn a:not(:last-child):after {
	content: '';
	width: 1.05em;
	height: 1.05em;
	margin: -.2em .3em -.2em -.4em;
	display: inline-block;
	border: 1px solid rgba(0,0,0,0.2);
	border-width: .2em .2em 0 0;
	transform: rotate(45deg);
}
#mn a:hover {
	color: #000;
	text-decoration: underline;
}

html .editor-toolbar>button.disabled {
	opacity: .35;
	pointer-events: none;
}
html .editor-toolbar>button.save.force-save {
	background: #f97;
}
.CodeMirror {
	background: #f7f7f7;
}



/* darkmode */
html.z .mdo,
html.z .CodeMirror {
	border-color: #222;
}
html.z,
html.z body,
html.z .CodeMirror {
	background: #222;
	color: #ccc;
}
html.z .CodeMirror-cursor {
	border-color: #fff;
}
html.z .CodeMirror-selected {
	box-shadow: 0 0 1px #0cf inset;
}
html.z .CodeMirror-selected,
html.z .CodeMirror-selectedtext {
	border-radius: .1em;
	background: #246;
	color: #fff;
}



html.z #mn a {
	color: #ccc;
}
html.z #mn a:not(:last-child):after {
	border-color: rgba(255,255,255,0.3);
}
html.z .editor-toolbar {
	border-color: #2c2c2c;
	background: #1c1c1c;
}
html.z .editor-toolbar>i.separator {
	border-left: 1px solid #444;
	border-right: 1px solid #111;
}
html.z .editor-toolbar>button {
	margin-left: -1px; border: 1px solid rgba(255,255,255,0.1);
	color: #aaa;
}



html.z .editor-toolbar>button:hover {
	color: #333;
}
html.z .editor-toolbar>button.active {
	color: #333;
	border-color: #ec1;
	background: #c90;
}
html.z .editor-toolbar::after,
html.z .editor-toolbar::before {
	background: none;
}



/* ui.css overrides */
.mdo {
	padding: 1em;
	background: #f7f7f7;
}
html.z .mdo {
	background: #1c1c1c;
}
