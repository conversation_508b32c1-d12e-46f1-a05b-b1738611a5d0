:root {
	--font-main: sans-serif;
	--font-serif: serif;
	--font-mono: 'scp';

	--fg: #ccc;
	--fg-max: #fff;
	--bg-u2: #2b2b2b;
	--bg-u5: #444;
}
html.y {
	--fg: #222;
	--fg-max: #000;
	--bg-u2: #f7f7f7;
	--bg-u5: #ccc;
}
html.bz {
	--bg-u2: #202231;
}
@font-face {
	font-family: 'scp';
	font-display: swap;
	src: local('Source Code Pro Regular'), local('SourceCodePro-Regular'), url(deps/scp.woff2) format('woff2');
}
html {
	text-size-adjust: 100%;
	-webkit-text-size-adjust: 100%;
	touch-action: manipulation;
}
#tt, #toast {
	position: fixed;
	max-width: 34em;
	max-width: min(34em, 90%);
	max-width: min(34em, calc(100% - 7em));
	color: #ddd;
	color: var(--fg);
	background: #333;
	background: var(--bg-u2);
	border: 0 solid #777;
	box-shadow: 0 .2em .5em #111;
	border-radius: .4em;
	z-index: 9001;
}
#tt {
	max-width: min(34em, calc(100% - 3.3em));
	overflow: hidden;
	margin: .7em 0;
	padding: 0 1.3em;
	height: 0;
	opacity: .1;
	transition: opacity 0.14s, height 0.14s, padding 0.14s;
}
#toast {
	bottom: 5em;
	right: -1em;
	line-height: 1.5em;
	padding: 1em 1.3em;
	margin-left: 3em;
	border-width: .4em 0;
	overflow-wrap: break-word;
	transform: translateX(100%);
	transition:
		transform .4s cubic-bezier(.2, 1.2, .5, 1),
		right .4s cubic-bezier(.2, 1.2, .5, 1);
	text-shadow: 1px 1px 0 #000;
	color: #fff;
}
#toast.top {
	top: 2em;
	bottom: unset;
}
#toastt {
	position: absolute;
	height: 1px;
	top: 1px;
	right: 1px;
	left: 1px;
	animation: toastt var(--tmtime) 0.07s steps(var(--tmstep)) forwards;
	transform-origin: right;
}
@keyframes toastt {
	to {transform: scaleX(0)}
}
#toast a {
	color: inherit;
	text-shadow: inherit;
	background: rgba(0, 0, 0, 0.4);
	border-radius: .3em;
	padding: .2em .3em;
}
#toast a#toastc {
	display: inline-block;
	position: absolute;
	overflow: hidden;
	left: 0;
	width: 0;
	opacity: 0;
	padding: .3em 0;
	margin: -.3em 0 0 0;
	line-height: 1.3em;
	color: #000;
	border: none;
	outline: none;
	text-shadow: none;
	border-radius: .5em 0 0 .5em;
	transition: left .3s, width .3s, padding .3s, opacity .3s;
}
#toastb {
	max-height: 70vh;
	overflow-y: auto;
	padding: .1em;
}
#toast.scroll #toastb {
	overflow-y: scroll;
	margin-right: -1.2em;
	padding-right: .7em;
}
#toast.r #toastb {
	text-align: right;
}
#toast pre {
	margin: 0;
}
#toast.hide {
	display: none;
}
#toast.vis {
	right: 1.3em;
	transform: inherit;
	transform: initial;
}
#toast.vis #toastc {
	left: -2em;
	width: .4em;
	padding: .3em .8em;
	opacity: 1;
}
#toast.inf {
	background: #07a;
	border-color: #0be;
}
#toast.inf #toastc {
	background: #0be;
}
#toast.inf #toastt {
	background: #8ef;
}
#toast.ok {
	background: #380;
	border-color: #8e4;
}
#toast.ok #toastc {
	background: #8e4;
}
#toast.ok #toastt {
	background: #cf9;
}
#toast.warn {
	background: #960;
	border-color: #fc0;
}
#toast.warn #toastc {
	background: #fc0;
}
#toast.warn #toastt {
	background: #fe9;
}
#toast.err {
	background: #900;
	border-color: #d06;
}
#toast.err #toastc {
	background: #d06;
}
#toast.err #toastt {
	background: #f9c;
}
#toast code {
	padding: 0 .2em;
	background: rgba(0,0,0,0.2);
}
#tth {
	color: #fff;
	background: #111;
	font-size: .9em;
	padding: 0 .26em;
	line-height: .97em;
	border-radius: 1em;
	position: absolute;
	display: none;
}
#tth.act {
	display: block;
	z-index: 9001;
}
#tt.b {
	padding: 0 2em;
	border-radius: .5em;
	box-shadow: 0 .2em 1em #000;
}
#tt.show {
	padding: 1em 1.3em;
	border-width: .4em 0;
	height: auto;
	opacity: 1;
}
#tt.show.b {
	padding: 1.5em 2em;
	border-width: .5em 0;
}
.logue code,
#modalc code,
#tt code {
	color: #eee;
	color: var(--fg-max);
	background: #444;
	background: var(--bg-u5);
	padding: .1em .3em;
	border-radius: .3em;
	line-height: 1.7em;
}
#tt em {
	color: #f6a;
}
html.y #tt {
	border-color: #888 #000 #777 #000;
}
html.bz #tt {
	border-color: #3b3f58;
}
html.y #tt,
html.y #toast {
	box-shadow: 0 .3em 1em rgba(0,0,0,0.4);
}
#modalc code {
	color: #060;
	background: transparent;
	border: 1px solid #ccc;
}
html.y #tt em {
	color: #d38;
}
html.y #tth {
	color: #000;
	background: #fff;
}
#cf_frame {
	position: fixed;
	z-index: 573;
	top: 3em;
	left: 50%;
	width: 40em;
	height: 30em;
	margin-left: -20.2em;
	border-radius: .4em;
	border: .4em solid var(--fg);
	box-shadow: 0 2em 4em 1em var(--bg-max);
}
#hkhelp,
#modal {
	position: fixed;
    overflow: auto;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
	z-index: 9001;
	background: rgba(64,64,64,0.6);
}
#modal>table {
    width: 100%;
    height: 100%;
}
#modal td {
	text-align: center;
}
#modalc {
	position: relative;
	display: inline-block;
	background: #f7f7f7;
	color: #333;
	text-shadow: none;
	text-align: left;
	margin: 3em;
	padding: 1em 1.1em;
	border-radius: .6em;
    box-shadow: 0 .3em 3em rgba(0,0,0,0.5);
	max-width: 50em;
	max-height: 30em;
	overflow-x: auto;
	overflow-y: scroll;
}
#modalc.yk {
	overflow-y: auto;
}
#modalc td {
	text-align: unset;
	padding: .2em;
}
@media (min-width: 40em) {
    #modalc {
        min-width: 30em;
    }
}
#modalc li {
	margin: 1em 0;
}
#modalc h6 {
	font-size: 1.3em;
	border-bottom: 1px solid #999;
	margin: 0;
	padding: .3em;
	text-align: center;
}
#modalc a {
	color: #07b;
}
#modalc .b64 {
	display: block;
	margin: .1em auto;
	width: 60%;
	height: 60%;
	background: #999;
	background: rgba(128,128,128,0.2);
}
#modalb {
	position: sticky;
	text-align: right;
	padding-top: 1em;
	bottom: 0;
	right: 0;
}
#modalb a {
	color: #000;
	background: #ccc;
	display: inline-block;
	border-radius: .3em;
	padding: .5em 1em;
	outline: none;
	border: none;
}
#modalb a:focus,
#modalb a:hover {
	background: #06d;
	color: #fff;
}
#modalb a+a {
	margin-left: .5em;
}
#modali {
	display: block;
	background: #fff;
	color: #000;
	width: calc(100% - 1.25em);
    margin: 1em -.1em 0 -.1em;
	padding: .5em;
	outline: none;
	border: .25em solid #ccc;
	border-radius: .4em;
}
#modali:focus {
	border-color: #06d;
}
#repl_pre {
	max-width: 24em;
}
*:focus,
*:focus+label,
#pctl *:focus,
.btn:focus {
	box-shadow: 0 .1em .2em #fc0 inset;
	outline: #fc0 solid .1em;
	border-radius: .2em;
}
html.y *:focus,
html.y *:focus+label,
html.y #pctl *:focus,
html.y .btn:focus {
	box-shadow: 0 .1em .2em #037 inset;
	outline: #037 solid .1em;
}
input, button {
	font-family: var(--font-main), sans-serif;
}
input[type="submit"] {
	cursor: pointer;
}
input[type="text"]:focus,
input:not([type]):focus,
textarea:focus {
	box-shadow: 0 .1em .3em #fc0, 0 -.1em .3em #fc0;
}
html.y input[type="text"]:focus,
html.y input:not([type]):focus,
html.y textarea:focus {
	box-shadow: 0 .1em .3em #037, 0 -.1em .3em #037;
}











.mdo pre,
.mdo code,
.mdo a {
	color: #480;
	background: #f7f7f7;
	border: .07em solid #ddd;
	border-radius: .2em;
	padding: .1em .3em;
	margin: 0 .1em;
}
.mdo pre,
.mdo code,
.mdo code[class*="language-"],
.mdo tt {
	font-family: 'scp', monospace, monospace;
	font-family: var(--font-mono), 'scp', monospace, monospace;
	white-space: pre-wrap;
	word-break: break-all;
}
.mdo code {
	font-size: .96em;
}
.mdo h1,
.mdo h2 {
	line-height: 1.5em;
}
.mdo h1 {
	font-size: 1.7em;
	text-align: center;
	border: 1em solid #777;
	border-width: .05em 0;
	margin: 3em 0;
}
.mdo h2 {
	font-size: 1.5em;
	font-weight: normal;
	background: #f7f7f7;
	border-top: .07em solid #fff;
	border-bottom: .07em solid #bbb;
	border-radius: .5em .5em 0 0;
	padding-left: .4em;
	margin-top: 3em;
}
.mdo h3 {
	border-bottom: .1em solid #999;
}
.mdo h1 a, .mdo h3 a, .mdo h5 a,
.mdo h2 a, .mdo h4 a, .mdo h6 a {
	color: inherit;
	display: block;
	background: none;
	border: none;
	padding: 0;
	margin: 0;
}
.mdo ul,
.mdo ol {
	padding-left: 1em;
}
.mdo ul ul,
.mdo ul ol,
.mdo ol ul,
.mdo ol ol {
	padding-left: 2em;
	border-left: .3em solid #ddd;
}
.mdo ul>li {
	margin: .7em 0;
	list-style-type: disc;
}
.mdo ol>li {
	margin: .7em 0 .7em 2em;
}
.mdo strong {
	color: #000;
}
.mdo p>em,
.mdo li>em,
.mdo td>em {
	color: #c50;
	padding: .1em;
	border-bottom: .1em solid #bbb;
}
.mdo blockquote {
	font-family: serif;
	font-family: var(--font-serif), serif;
	background: #f7f7f7;
	border: .07em dashed #ccc;
	padding: 0 2em;
	margin: 1em 0;
}
.mdo small {
	opacity: .8;
}
.mdo pre code {
	display: block;
	margin: 0 -.3em;
	padding: .4em .5em;
	line-height: 1.1em;
}
.mdo pre code:hover {
	background: #fec;
	color: #360;
}
.mdo table {
	border-collapse: collapse;
	margin: 1em 0;
}
.mdo th,
.mdo td {
	padding: .2em .5em;
	border: .12em solid #aaa;
}
.mdo .mdth,
.mdo .mdthl,
.mdo .mdthr {
	margin: .5em .5em .5em 0;
}
.mdthl {
	float: left;
}
.mdthr {
	float: right;
}
hr {
	clear: both;
}

@media screen {
	.mdo {
		word-break: break-word;
		overflow-wrap: break-word;
		word-wrap: break-word; /*ie*/
	}
	html.y .mdo a,
	.mdo a {
		color: #fff;
		background: #39b;
		text-decoration: none;
		padding: 0 .3em;
		border: none;
		border-bottom: .07em solid #079;
	}
	.mdo h1 {
		color: #fff;
		background: #444;
		font-weight: normal;
		border-top: .4em solid #fb0;
		border-bottom: .4em solid #777;
		border-radius: 0 1em 0 1em;
		margin: 3em 0 1em 0;
		padding: .5em 0;
	}
	.mdo h2 {
		color: #fff;
		background: #555;
		margin-top: 2em;
		border-bottom: .22em solid #999;
		border-top: none;
	}



	html.z .mdo a {
		background: #057;
	}
	html.z .mdo h1 a, html.z .mdo h4 a,
	html.z .mdo h2 a, html.z .mdo h5 a,
	html.z .mdo h3 a, html.z .mdo h6 a {
		color: inherit;
		background: none;
	}
	html.z .mdo pre,
	html.z .mdo code {
		color: #8c0;
		background: #1a1a1a;
		border: .07em solid #333;
	}
	html.z .mdo ul,
	html.z .mdo ol {
		border-color: #444;
	}
	html.z .mdo strong {
		color: #fff;
	}
	html.z .mdo p>em,
	html.z .mdo li>em,
	html.z .mdo td>em {
		color: #f94;
		border-color: #666;
	}
	html.z .mdo h1 {
		background: #383838;
		border-top: .4em solid #b80;
		border-bottom: .4em solid #4c4c4c;
	}
	html.bz .mdo h1 {
		background: #202231;
		border: 1px solid #2d2f45;
		border-width: 0 0 .4em 0;
	}
	html.z .mdo h2 {
		background: #444;
		border-bottom: .22em solid #555;
	}
	html.bz .mdo h2,
	html.bz .mdo h3 {
		background: transparent;
		border-color: #3b3f58;
	}
	html.z .mdo td,
	html.z .mdo th {
		border-color: #444;
	}
	html.z .mdo blockquote {
		background: #282828;
		border: .07em dashed #444;
	}
}

@media (prefers-reduced-motion) {
	#toast,
	#toast a#toastc,
	#tt {
		transition: none;
	}
}