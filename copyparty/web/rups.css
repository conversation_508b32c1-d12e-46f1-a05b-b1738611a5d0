html {
	color: #333;
	background: #f7f7f7;
	font-family: sans-serif;
	font-family: var(--font-main), sans-serif;
	touch-action: manipulation;
}
#wrap {
	margin: 2em auto;
	padding: 0 1em 3em 1em;
	line-height: 2.3em;
}
a {
	color: #047;
	background: #fff;
	text-decoration: none;
	border-bottom: 1px solid #8ab;
	border-radius: .2em;
	padding: .2em .6em;
	margin: 0 .3em;
}
#wrap td a {
	margin: 0;
	line-height: 1em;
	display: inline-block;
	white-space: initial;
	font-family: var(--font-main), sans-serif;
}
#repl {
	border: none;
	background: none;
	color: inherit;
	padding: 0;
	position: fixed;
	bottom: .25em;
	left: .2em;
}
#wrap table {
	border-collapse: collapse;
	position: relative;
	margin-top: 2em;
}
#wrap th {
	top: -1px;
	position: sticky;
	background: #f7f7f7;
}
#wrap td {
	font-family: var(--font-mono), monospace, monospace;
	white-space: pre; /*date*/
	overflow: hidden; /*ipv6*/
}
#wrap th:first-child,
#wrap td:first-child {
	text-align: right;
}
#wrap td,
#wrap th {
	text-align: left;
	padding: .3em .6em;
	max-width: 30vw;
}
#wrap tr:hover td {
	background: #ddd;
	box-shadow: 0 -1px 0 rgba(128, 128, 128, 0.5) inset;
}
#wrap th:first-child,
#wrap td:first-child {
	border-radius: .5em 0 0 .5em;
}
#wrap th:last-child,
#wrap td:last-child {
	border-radius: 0 .5em .5em 0;
}



html.z {
	background: #222;
	color: #ccc;
}
html.bz {
	background: #11121d;
	color: #bbd;
}
html.z a {
	color: #fff;
	background: #057;
	border-color: #37a;
}
html.z input[type=text] {
	color: #ddd;
	background: #223;
	border: none;
	border-bottom: 1px solid #fc5;
	border-radius: .2em;
	padding: .2em .3em;
}
html.z #wrap th {
	background: #222;
}
html.bz #wrap th {
	background: #223;
}
html.z #wrap tr:hover td {
	background: #000;
}
