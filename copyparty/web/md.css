html, body {
	color: #333;
	background: #eee;
	font-family: sans-serif;
	font-family: var(--font-main), sans-serif;
	line-height: 1.5em;
}
html.y #helpbox a {
	color: #079;
}
html.z #helpbox a {
	color: #fc5;
}
#repl {
	position: absolute;
	top: 0;
	right: .5em;
	border: none;
	color: inherit;
	background: none;
}
#mtw {
	display: none;
}
#mw {
	margin: 0 auto;
	padding: 0 1.5em;
}
#toast {
	bottom: auto;
	top: 1.4em;
}
a {
	text-decoration: none;
}
#toc {
	margin: 0 1em;
	-ms-scroll-chaining: none;
	overscroll-behavior-y: none;
}
#toc ul {
	padding-left: 1em;
}
#toc>ul {
	text-align: left;
	padding-left: .5em;
}
#toc li {
	list-style-type: none;
	line-height: 1.2em;
	margin: .5em 0;
}
#toc a {
	color: #057;
	border: none;
	background: none;
	display: block;
	margin-left: -.3em;
	padding: .2em .3em;
}
#toc a.act {
	color: #fff;
	background: #07a;
}
.todo_pend,
.todo_done {
	z-index: 99;
	position: relative;
	display: inline-block;
	font-family: 'scp', monospace, monospace;
	font-family: var(--font-mono), 'scp', monospace, monospace;
	font-weight: bold;
	font-size: 1.3em;
	line-height: .1em;
	margin: -.5em 0 -.5em -.85em;
	top: .1em;
	color: #b29;
}
.todo_done {
	color: #6b3;
	text-shadow: .02em 0 0 #6b3;
}
blink {
	animation: blinker .7s cubic-bezier(.9, 0, .1, 1) infinite;
}
@keyframes blinker {
	10% {
		opacity: 0;
	}
	60% {
		opacity: 1;
	}
}


.mdo pre {
	counter-reset: precode;
}
.mdo pre code {
	counter-increment: precode;
	display: inline-block;
	border: none;
	border-bottom: 1px solid #cdc;
	min-width: calc(100% - .6em);
}
.mdo pre code:last-child {
	border-bottom: none;
}
.mdo pre code::before {
	content: counter(precode);
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	display: inline-block;
	text-align: right;
	font-size: .75em;
	color: #48a;
	width: 4em;
	padding-right: 1.5em;
	margin-left: -5.5em;
}


@media screen {
	html, body {
		margin: 0;
		padding: 0;
		outline: 0;
		border: none;
		width: 100%;
		height: 100%;
	}
	#mw {
		margin: 0 auto;
		right: 0;
	}
	#mp {
		max-width: 52em;
		margin-bottom: 6em;
	}
	#mn {
		padding: 1.3em 0 .7em 1em;
		border-bottom: 1px solid #ccc;
		background: #eee;
		z-index: 10;
		width: calc(100% - 1em);
	}
	#mn a {
		color: #444;
		background: none;
		margin: 0 0 0 -.2em;
		padding: .3em 0 .3em .4em;
		text-decoration: none;
		border: none;
		/* ie: */
		border-bottom: .1em solid #777\9;
		margin-right: 1em\9;
	}
	#mn a:first-child {
		padding-left: .5em;
	}
	#mn a:last-child {
		padding-right: .5em;
	}
	#mn a:not(:last-child)::after {
		content: '';
		width: 1.05em;
		height: 1.05em;
		margin: -.2em .3em -.2em -.4em;
		display: inline-block;
		border: 1px solid rgba(154,154,154,0.6);
		border-width: .2em .2em 0 0;
		transform: rotate(45deg);
	}
	#mn a:hover {
		color: #000;
		text-decoration: underline;
	}
	#mh {
		padding: .4em 1em;
		position: relative;
		width: 100%;
		width: calc(100% - 3em);
		background: #eee;
		z-index: 9;
		top: 0;
	}
	#mh a {
		color: #444;
		background: none;
		text-decoration: underline;
		margin: 0 .1em;
		padding: 0 .3em;
		border: none;
	}
	#mh a:hover {
		color: #000;
		background: #ddd;
	}
	#toolsbox {
		overflow: hidden;
		display: inline-block;
		background: #eee;
		height: 1.5em;
		padding: 0 .2em;
		margin: 0 .2em;
		position: absolute;
	}
	#toolsbox.open {
		height: auto;
		overflow: visible;
		background: #eee;
		box-shadow: 0 .2em .2em #ccc;
		padding-bottom: .2em;
	}
	#toolsbox a {
		display: block;
	}
	#toolsbox a+a {
		text-decoration: none;
	}
	#lno {
		position: absolute;
		right: 0;
	}



	html.z,
	html.z body {
		background: #222;
		color: #ccc;
	}
	html.z #toc a {
		color: #ccc;
		border-left: .4em solid #444;
		border-bottom: .1em solid #333;
	}
	html.z #toc a.act {
		color: #fff;
		border-left: .4em solid #3ad;
	}
	html.z #toc li {
		border-width: 0;
	}
	html.z #mn a {
		color: #ccc;
	}
	html.z #mn {
		border-bottom: 1px solid #333;
	}
	html.z #mn,
	html.z #mh {
		background: #222;
	}
	html.z #mh a {
		color: #ccc;
		background: none;
	}
	html.z #mh a:hover {
		background: #333;
		color: #fff;
	}
	html.z #toolsbox {
		background: #222;
	}
	html.z #toolsbox.open {
		box-shadow: 0 .2em .2em #069;
		border-radius: 0 0 .4em .4em;
	}
}

@media screen and (min-width: 66em) {
	#mw {
		position: fixed;
		overflow-y: auto;
		left: 14em;
		left: calc(100% - 55em);
		max-width: none;
		bottom: 0;
		scrollbar-color: #eb0 #f7f7f7;
	}
	#toc {
		width: 13em;
		width: calc(100% - 55.3em);
		max-width: 30em;
		background: #eee;
		position: fixed;
		overflow-y: auto;
		top: 0;
		left: 0;
		bottom: 0;
		padding: 0;
		margin: 0;
		scrollbar-color: #eb0 #f7f7f7;
		box-shadow: 0 0 1em rgba(0,0,0,0.1);
		border-top: 1px solid #d7d7d7;
	}
	#toc li {
		border-left: .3em solid #ccc;
	}
	#toc::-webkit-scrollbar-track {
		background: #f7f7f7;
	}
	#toc::-webkit-scrollbar {
		background: #f7f7f7;
		width: .8em;
	}
	#toc::-webkit-scrollbar-thumb {
		background: #eb0;
	}



	html.z #toc {
		background: #282828;
		border-top: 1px solid #2c2c2c;
		box-shadow: 0 0 1em #181818;
	}
	html.z #toc,
	html.z #mw {
		scrollbar-color: #b80 #282828;
	}
	html.z #toc::-webkit-scrollbar-track {
		background: #282828;
	}
	html.z #toc::-webkit-scrollbar {
		background: #282828;
		width: .8em;
	}
	html.z #toc::-webkit-scrollbar-thumb {
		background: #b80;
	}
}
@media screen and (min-width: 85.5em) {
	#toc { width: 30em }
	#mw { left: 30.5em }
}
@media print {
	@page {
		size: A4;
		padding: 0;
		margin: .5in .6in;
		mso-header-margin: .6in;
		mso-footer-margin: .6in;
		mso-paper-source: 0;
	}
	.mdo a {
		color: #079;
		text-decoration: none;
		border-bottom: .07em solid #4ac;
		padding: 0 .3em;
	}
	#repl {
		display: none;
	}
	#toc>ul {
		border-left: .1em solid #84c4dd;
	}
	#mn, #mh {
		display: none;
	}
	html, body, #toc, #mw {
		margin: 0 !important;
		word-break: break-word;
		width: 52em;
	}
	#toc {
		margin-left: 1em !important;
	}
	#toc a {
		color: #000 !important;
	}
	#toc a::after {
		/* hopefully supported by browsers eventually */
		content: leader('.') target-counter(attr(href), page);
	}
	a[ctr]::before {
		content: attr(ctr) '. ';
	}
	.mdo h1 {
		margin: 2em 0;
	}
	.mdo h2 {
		margin: 2em 0 0 0;
	}
	.mdo h1,
	.mdo h2,
	.mdo h3 {
		page-break-inside: avoid;
	}
	.mdo h1::after,
	.mdo h2::after,
	.mdo h3::after {
		content: 'orz';
		color: transparent;
		display: block;
		line-height: 1em;
		padding: 4em 0 0 0;
		margin: 0 0 -5em 0;
	}
	.mdo p {
		page-break-inside: avoid;
	}
	.mdo table {
		page-break-inside: auto;
	}
	.mdo tr {
		page-break-inside: avoid;
		page-break-after: auto;
	}
	.mdo thead {
		display: table-header-group;
	}
	.mdo tfoot {
		display: table-footer-group;
	}
	#mp a.vis::after {
		content: ' (' attr(href) ')';
		border-bottom: 1px solid #bbb;
		color: #444;
	}
	.mdo blockquote {
		border-color: #555;
	}
	.mdo code {
		border-color: #bbb;
	}
	.mdo pre,
	.mdo pre code {
		border-color: #999;
	}
	.mdo pre code::before {
		color: #058;
	}



	html.z .mdo a {
		color: #000;
	}
	html.z .mdo pre,
	html.z .mdo code {
		color: #240;
	}
	html.z .mdo p>em,
	html.z .mdo li>em,
	html.z .mdo td>em {
		color: #940;
	}
}
