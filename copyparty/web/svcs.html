<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<title>{{ s_doctitle }}</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=0.8">
	<meta name="theme-color" content="#{{ tcolor }}">
	<link rel="stylesheet" media="screen" href="{{ r }}/.cpr/splash.css?_={{ ts }}">
	<link rel="stylesheet" media="screen" href="{{ r }}/.cpr/ui.css?_={{ ts }}">
	<style>ul{padding-left:1.3em}li{margin:.4em 0}.txa{float:right;margin:0 0 0 1em}</style>
{{ html_head }}
</head>

<body>
	<div id="wrap" class="w">
        <div class="cn">
            <p class="btns"><a href="/{{ rvp }}">browse files</a> // <a href="{{ r }}/?h">control panel</a></p>
            <p>or choose your OS for cooler alternatives:</p>
            <div class="ossel">
                <a id="swin" href="#">Windows</a>
                <a id="slin" href="#">Linux</a>
                <a id="smac" href="#">macOS</a>
            </div>
        </div>

        <p class="sph">
            make this server appear on your computer as a regular HDD!<br />
            pick your favorite below (sorted by performance, best first) and lets 🎉<br />
            <br />
            <span class="os win lin mac">placeholders:</span>
            <span class="os win">
                {% if accs %}<code><b id="pw0">{{ pw }}</b></code>=password, {% endif %}<code><b>W:</b></code>=mountpoint
            </span>
            <span class="os lin mac">
                {% if accs %}<code><b id="pw0">{{ pw }}</b></code>=password, {% endif %}<code><b>mp</b></code>=mountpoint
            </span>
            {% if accs %}<a href="#" id="setpw">use real password</a>{% endif %}
        </p>



        {% if args.idp_h_usr %}
        <p style="line-height:2em"><b>WARNING:</b> this server is using IdP-based authentication, so this stuff may not work as advertised. Depending on server config, these commands can probably only be used to access areas which don't require authentication, unless you auth using any non-IdP accounts defined in the copyparty config. Please see <a href="https://github.com/9001/copyparty/blob/hovudstraum/docs/idp.md#connecting-webdav-clients">the IdP docs</a></p>
        {% endif %}



        {% if not args.no_dav %}
        <h1>WebDAV</h1>

        <div class="os win">
            <p>if you can, install <a href="https://winfsp.dev/rel/">winfsp</a>+<a href="https://downloads.rclone.org/rclone-current-windows-amd64.zip">rclone</a> and then paste this in cmd:</p>
            <pre>
                rclone config create {{ aname }}-dav webdav url=http{{ s }}://{{ rip }}{{ hport }} vendor=owncloud pacer_min_sleep=0.01ms{% if accs %} user=k pass=<b>{{ pw }}</b>{% endif %}
                rclone mount --vfs-cache-mode writes --dir-cache-time 5s {{ aname }}-dav:{{ rvp }} <b>W:</b>
            </pre>
            <ul>
                {% if s %}
                <li>running <code>rclone mount</code> on LAN (or just dont have valid certificates)? add <code>--no-check-certificate</code></li>
                {% endif %}
                <li>old version of rclone? replace all <code>=</code> with <code>&nbsp;</code> (space)</li>
            </ul>

            <p>if you want to use the native WebDAV client in windows instead (slow and buggy), first run <a href="{{ r }}/.cpr/a/webdav-cfg.bat">webdav-cfg.bat</a> to remove the 47 MiB filesize limit (also fixes latency and password login), then connect:</p>
            <pre>
                net use <b>w:</b> http{{ s }}://{{ ep }}/{{ rvp }}{% if accs %} k /user:<b>{{ pw }}</b>{% endif %}
            </pre>
        </div>

        <div class="os lin">
            <p>rclone (v1.63 or later) is recommended:</p>
            <pre>
                rclone config create {{ aname }}-dav webdav url=http{{ s }}://{{ rip }}{{ hport }} vendor=owncloud pacer_min_sleep=0.01ms{% if accs %} user=k pass=<b>{{ pw }}</b>{% endif %}
                rclone mount --vfs-cache-mode writes --dir-cache-time 5s {{ aname }}-dav:{{ rvp }} <b>mp</b>
            </pre>
            <ul>
                {% if s %}
                <li>running <code>rclone mount</code> on LAN (or just dont have valid certificates)? add <code>--no-check-certificate</code></li>
                {% endif %}
                <li>running <code>rclone mount</code> as root? add <code>--allow-other</code></li>
                <li>old version of rclone? replace all <code>=</code> with <code>&nbsp;</code> (space)</li>
            </ul>
            <p>alternatively use davfs2 (requires root, is slower, forgets lastmodified-timestamp on upload):</p>
            <pre>
                yum install davfs2
                {% if accs %}printf '%s\n' <b>{{ pw }}</b> k | {% endif %}mount -t davfs -ouid=1000 http{{ s }}://{{ ep }}/{{ rvp }} <b>mp</b>
            </pre>
            <p>make davfs2 automount on boot:</p>
            <pre>
                printf '%s\n' "http{{ s }}://{{ ep }}/{{ rvp }} <b>{{ pw }}</b> k" >> /etc/davfs2/secrets
                printf '%s\n' "http{{ s }}://{{ ep }}/{{ rvp }} <b>mp</b> davfs rw,user,uid=1000,noauto 0 0" >> /etc/fstab
            </pre>
            <p>or the emergency alternative (gnome/gui-only):</p>
            <!-- gnome-bug: ignores vp -->
            <pre>
                {%- if accs %}
                echo <b>{{ pw }}</b> | gio mount dav{{ s }}://k@{{ ep }}/{{ rvp }}
                {%- else %}
                gio mount -a dav{{ s }}://{{ ep }}/{{ rvp }}
                {%- endif %}
            </pre>
            <p>on KDE Dolphin, use <code>webdav{{ s }}://{{ ep }}/{{ rvp }}</code></p>
        </div>

        <div class="os mac">
            <pre>
                osascript -e ' mount volume "http{{ s }}://k:<b>{{ pw }}</b>@{{ ep }}/{{ rvp }}" '
            </pre>
            <p>or you can open up a Finder, press command-K and paste this instead:</p>
            <pre>
                http{{ s }}://k:<b>{{ pw }}</b>@{{ ep }}/{{ rvp }}
            </pre>

            {% if s %}
            <p><em>replace <code>https</code> with <code>http</code> if it doesn't work</em></p>
            {% endif %}
        </div>
        {% endif %}



        {% if args.ftp or args.ftps %}
        <h1>FTP</h1>

        <div class="os win">
            <p>if you can, install <a href="https://winfsp.dev/rel/">winfsp</a>+<a href="https://downloads.rclone.org/rclone-current-windows-amd64.zip">rclone</a> and then paste this in cmd:</p>
            {% if args.ftp %}
            <p>connect with plaintext FTP:</p>
            <pre>
                rclone config create {{ aname }}-ftp ftp host={{ rip }} port={{ args.ftp }} pass=k user={% if accs %}<b>{{ pw }}</b>{% else %}anonymous{% endif %} tls=false
                rclone mount --vfs-cache-mode writes --dir-cache-time 5s {{ aname }}-ftp:{{ rvp }} <b>W:</b>
            </pre>
            {% endif %}
            {% if args.ftps %}
            <p>connect with TLS-encrypted FTPS:</p>
            <pre>
                rclone config create {{ aname }}-ftps ftp host={{ rip }} port={{ args.ftps }} pass=k user={% if accs %}<b>{{ pw }}</b>{% else %}anonymous{% endif %} tls=false explicit_tls=true
                rclone mount --vfs-cache-mode writes --dir-cache-time 5s {{ aname }}-ftps:{{ rvp }} <b>W:</b>
            </pre>
            {% endif %}
            <ul>
                {% if args.ftps %}
                <li>running on LAN (or just dont have valid certificates)? add <code>no_check_certificate=true</code> to the config command</li>
                {% endif %}
                <li>old version of rclone? replace all <code>=</code> with <code>&nbsp;</code> (space)</li>
            </ul>
            <p>if you want to use the native FTP client in windows instead (please dont), press <code>win+R</code> and run this command:</p>
            <pre>
                explorer {{ "ftp" if args.ftp else "ftps" }}://{% if accs %}<b>{{ pw }}</b>:k@{% endif %}{{ host }}:{{ args.ftp or args.ftps }}/{{ rvp }}
            </pre>
        </div>

        <div class="os lin">
            {% if args.ftp %}
            <p>connect with plaintext FTP:</p>
            <pre>
                rclone config create {{ aname }}-ftp ftp host={{ rip }} port={{ args.ftp }} pass=k user={% if accs %}<b>{{ pw }}</b>{% else %}anonymous{% endif %} tls=false
                rclone mount --vfs-cache-mode writes --dir-cache-time 5s {{ aname }}-ftp:{{ rvp }} <b>mp</b>
            </pre>
            {% endif %}
            {% if args.ftps %}
            <p>connect with TLS-encrypted FTPS:</p>
            <pre>
                rclone config create {{ aname }}-ftps ftp host={{ rip }} port={{ args.ftps }} pass=k user={% if accs %}<b>{{ pw }}</b>{% else %}anonymous{% endif %} tls=false explicit_tls=true
                rclone mount --vfs-cache-mode writes --dir-cache-time 5s {{ aname }}-ftps:{{ rvp }} <b>mp</b>
            </pre>
            {% endif %}
            <ul>
                {% if args.ftps %}
                <li>running on LAN (or just dont have valid certificates)? add <code>no_check_certificate=true</code> to the config command</li>
                {% endif %}
                <li>running <code>rclone mount</code> as root? add <code>--allow-other</code></li>
                <li>old version of rclone? replace all <code>=</code> with <code>&nbsp;</code> (space)</li>
            </ul>
            <p>emergency alternative (gnome/gui-only):</p>
            <!-- gnome-bug: ignores vp -->
            <pre>
                {%- if accs %}
                echo <b>{{ pw }}</b> | gio mount ftp{{ "" if args.ftp else "s" }}://k@{{ host }}:{{ args.ftp or args.ftps }}/{{ rvp }}
                {%- else %}
                gio mount -a ftp{{ "" if args.ftp else "s" }}://{{ host }}:{{ args.ftp or args.ftps }}/{{ rvp }}
                {%- endif %}
            </pre>
        </div>

        <div class="os mac">
            <p>note: FTP is read-only on macos; please use WebDAV instead</p>
            <pre>
                open {{ "ftp" if args.ftp else "ftps" }}://{% if accs %}k:<b>{{ pw }}</b>@{% else %}anonymous:@{% endif %}{{ host }}:{{ args.ftp or args.ftps }}/{{ rvp }}
            </pre>
        </div>
        {% endif %}



        <h1>partyfuse</h1>
        <p>
            <a href="{{ r }}/.cpr/a/partyfuse.py">partyfuse.py</a> -- fast, read-only,
            needs <a href="{{ r }}/.cpr/deps/fuse.py">fuse.py</a> in the same folder,
            <span class="os win">needs <a href="https://winfsp.dev/rel/">winfsp</a></span>
            <span class="os lin">doesn't need root</span>
        </p>
        <pre>
            partyfuse.py{% if accs %} -a <b>{{ pw }}</b>{% endif %} http{{ s }}://{{ ep }}/{{ rvp }} <b><span class="os win">W:</span><span class="os lin mac">mp</span></b>
        </pre>
        {% if s %}
        <ul><li>if you are on LAN (or just dont have valid certificates), add <code>-td</code></li></ul>
        {% endif %}
        <p>
            you can use <a href="{{ r }}/.cpr/a/u2c.py">u2c.py</a> to upload (sometimes faster than web-browsers)
        </p>


        {% if args.smb %}
        <h1>SMB / CIFS</h1>

        <div class="os win">
            <pre>
                net use <b>w:</b> \\{{ host }}\a{% if accs %} k /user:<b>{{ pw }}</b>{% endif %}
            </pre>
            <!-- rclone fails due to copyparty-smb bugs -->
        </div>

        <div class="os lin">
            <pre>
                mount -t cifs -o{% if accs %}user=<b>{{ pw }}</b>,pass=k,{% endif %}vers={{ 1 if args.smb1 else 2 }}.0,port={{ args.smb_port }},uid=1000 //{{ host }}/a/ <b>mp</b>
            </pre>
            <!-- p>or the emergency alternative (gnome/gui-only):</p nevermind, only works through mdns -->
        </div>

        <pre class="os mac">
            open 'smb://<b>{{ pw }}</b>:k@{{ host }}/a'
        </pre>
        {% endif %}



        <div class="os win">
            <h1>ShareX</h1>

            <p>to upload screenshots using ShareX <a href="https://getsharex.com/">v15+</a>, save this as <code>copyparty.sxcu</code> and run it:</p>

            <pre class="dl" name="copyparty.sxcu">
                { "Version": "15.0.0", "Name": "copyparty",
                "RequestURL": "http{{ s }}://{{ ep }}/{{ rvp }}",
                "Headers": {
                    {% if accs %}"pw": "<b>{{ pw }}</b>", {% endif %}"accept": "url"
                },
                "DestinationType": "ImageUploader, TextUploader, FileUploader",
                "Body": "MultipartFormData", "URL": "{response}",
                "RequestMethod": "POST", "FileFormName": "f" }
            </pre>

            <p>for ShareX <a href="https://github.com/ShareX/ShareX/releases/tag/v12.1.1">v12</a> specifically, save this as <code>copyparty.sxcu</code> and run it:</p>

            <pre class="dl" name="copyparty.sxcu">
                { "Name": "copyparty",
                "RequestURL": "http{{ s }}://{{ ep }}/{{ rvp }}",
                "Headers": {
                    {% if accs %}"pw": "<b>{{ pw }}</b>", {% endif %}"accept": "url"
                },
                "DestinationType": "ImageUploader, TextUploader, FileUploader",
                "FileFormName": "f" }
            </pre>
        </div>



        <div class="os mac">
            <h1>ishare</h1>

            <p>to upload screenshots using <a href="https://isharemac.app/">ishare</a>, save this as <code>copyparty.iscu</code> and run it:</p>

            <pre class="dl" name="copyparty.iscu">
                { "Name": "copyparty",
                "RequestURL": "http{{ s }}://{{ ep }}/{{ rvp }}",
                "Headers": {
                    {% if accs %}"pw": "<b>{{ pw }}</b>",{% endif %}
                    "accept": "json"
                },
                "ResponseURL": "{{ '{{fileurl}}' }}",
                "FileFormName": "f" }
            </pre>
        </div>



        <div class="os lin">
            <h1>flameshot</h1>

            <p>to upload screenshots using <a href="https://flameshot.org/">flameshot</a>, save this as <code>flameshot.sh</code> and run it:</p>

            <pre class="dl" name="flameshot.sh">
                #!/bin/bash
                pw="<b>{{ pw }}</b>"
                url="http{{ s }}://{{ ep }}/{{ rvp }}"
                filename="$(date +%Y-%m%d-%H%M%S).png"
                flameshot gui -s -r | curl -sT- "$url$filename?want=url&pw=$pw" | xsel -ib
            </pre>
        </div>



    </div>
	<a href="#" id="repl">π</a>
	<script>

var SR="{{ r }}",
    lang="{{ lang }}",
	dfavico="{{ favico }}";

var STG = window.localStorage;
document.documentElement.className = (STG && STG.cpp_thm) || "{{ args.theme }}";

</script>
<script src="{{ r }}/.cpr/util.js?_={{ ts }}"></script>
<script src="{{ r }}/.cpr/svcs.js?_={{ ts }}"></script>
{%- if js %}
<script src="{{ js }}_={{ ts }}"></script>
{%- endif %}
</body>
</html>

