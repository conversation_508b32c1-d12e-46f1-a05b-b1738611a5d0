:root {
	color-scheme: dark;

	--grid-sz: 10em;
	--grid-ln: 3;
	--nav-sz: 16em;
	--sbw: 0.5em;
	--sbh: 0.5em;

	--fg: #ccc;
	--fg-max: #fff;
	--fg2-max: #fff;
	--fg-weak: #bbb;

	--bg-u6: #4c4c4c;
	--bg-u5: #444;
	--bg-u4: #383838;
	--bg-u3: #333;
	--bg-u2: #2b2b2b;
	--bg-u1: #282828;
	--bg: #222;
	--bgg: var(--bg);
	--bg-d1: #1c1c1c;
	--bg-d2: #181818;
	--bg-d3: #111;
	--bg-max: #000;

	--tab-alt: #f5a;
	--row-alt: #282828;

	--scroll: #eb0;
	--sel-fg: var(--bg-d1);
	--sel-bg: var(--fg);

	--a: #fc5;
	--a-b: #c90;
	--a-hil: #fd9;
	--a-dark: #e70;
	--a-gray: #666;

	--btn-fg: var(--a);
	--btn-bg: rgba(128,128,128,0.15);
	--btn-h-fg: var(--a-hil);
	--btn-h-bg: #805;
	--btn-1-fg: #400;
	--btn-1-bg: var(--a);
	--btn-h-bs: var(--btn-bs);
	--btn-h-bb: var(--btn-bb);
	--btn-1-bs: var(--btn-bs);
	--btn-1-bb: var(--btn-bb);
	--btn-1h-fg: var(--btn-1-fg);
	--btn-1h-bg: #fe8;
	--btn-1h-bs: var(--btn-1-bs);
	--btn-1h-bb: var(--btn-1-bb);
	--chk-fg: var(--tab-alt);
	--txt-sh: var(--bg-d2);
	--txt-bg: var(--btn-bg);

	--op-aa-fg: var(--a);
	--op-aa-bg: var(--bg-d2);
	--op-a-sh: rgba(0,0,0,0.5);

	--u2-btn-b1: #999;
	--u2-sbtn-b1: #999;
	--u2-txt-bg: var(--bg-u5);
	--u2-tab-bg: linear-gradient(to bottom, var(--bg), var(--bg-u1));
	--u2-tab-b1: rgba(128,128,128,0.8);
	--u2-tab-1-fg: #fd7;
	--u2-tab-1-bg: linear-gradient(to bottom, #353, var(--bg) 80%);
	--u2-tab-1-b1: #7c5;
	--u2-tab-1-b2: #583;
	--u2-tab-1-sh: #280;
	--u2-b-fg: #fff;
	--u2-b1-bg: #c38;
	--u2-b2-bg: #d80;
	--u2-inf-bg: #07a;
	--u2-inf-b1: #0be;
	--u2-ok-bg: #380;
	--u2-ok-b1: #8e4;
	--u2-err-bg: #900;
	--u2-err-b1: #d06;
	--ud-b1: #888;

	--sort-1: #fb0;
	--sort-2: #d09;

	--srv-1: #aaa;
	--srv-2: #a73;
	--srv-3: #f4c;
	--srv-3b: rgba(255,68,204,0.6);

	--tree-bg: #2b2b2b;

	--g-play-bg: #750;
	--g-play-b1: #c90;
	--g-play-b2: #da4;
	--g-play-sh: #b83;

	--g-sel-fg: #fff;
	--g-sel-bg: #925;
	--g-sel-b1: #e39;
	--g-sel-sh: #b36;
	--g-fsel-bg: #d39;
	--g-fsel-b1: #f4a;
	--g-fsel-ts: #804;
	--g-dfg: var(--srv-3);
	--g-fg: var(--a-hil);
	--g-bg: var(--bg-u2);
	--g-b1: var(--bg-u4);
	--g-b2: var(--bg-u5);
	--g-g1: var(--bg-u2);
	--g-g2: var(--bg-u5);
	--g-f-bg: var(--bg-u4);
	--g-f-b1: var(--bg-u5);
	--g-f-fg: var(--a-hil);
	--g-sh: rgba(0,0,0,0.3);

	--f-sh1: 0.33;
	--f-sh2: 0.02;
	--f-sh3: 0.2;
	--f-h-b1: rgba(128,128,128,0.7);

	--f-play-bg: #fc5;
	--f-play-fg: #000;
	--f-sel-sh: #fc0;
	--f-gray: #999;

	--fm-off: #f6c;
	--mp-sh: var(--bg-d3);
	--mp-b-bg: rgba(0,0,0,0.2);

	--err-fg: #fff;
	--err-bg: #a20;
	--err-b1: #f00;
	--err-ts: #500;
}
html.y {
	color-scheme: light;

	--fg: #222;
	--fg-max: #000;
	--fg-weak: #555;

	--bg-d3: #fff;
	--bg-d2: #fff;
	--bg-d1: #fff;
	--bg: #eaeaea;
	--bg-u1: #fff;
	--bg-u2: #f7f7f7;
	--bg-u3: #eaeaea;
	--bg-u4: #fff;
	--bg-u5: #ccc;
	--bg-u6: #ddd;
	--bg-max: #fff;

	--tab-alt: #c07;
	--row-alt: #f2f2f2;

	--scroll: #490;

	--a: #06a;
	--a-b: #08b;
	--a-hil: #058;
	--a-gray: #bbb;
	--a-dark: #c0f;

	--btn-fg: #555;
	--btn-h-fg: #222;
	--btn-h-bg: #caf;
	--btn-1-fg: #fff;
	--btn-1-bg: #4a0;
	--btn-1h-bg: #5c0;
	--chk-fg: var(--fg);
	--txt-sh: #aaa;
	--txt-bg: rgba(255,255,255,0.6);

	--op-a-sh: #fff;

	--u2-txt-bg: var(--bg-max);
	--u2-tab-1-sh: #0ad;
	--u2-tab-1-b1: #09c;
	--u2-tab-1-b2: #05a;
	--u2-tab-1-fg: var(--fg-max);
	--u2-tab-1-bg: inherit;
	--ud-b1: #bbb;

	--sort-1: #059;
	--sort-2: #f5d;

	--srv-1: #555;
	--srv-2: #c83;
	--srv-3: #c0a;

	--tree-bg: #fff;

	--g-fg: var(--a);
	--g-bg: var(--bg-u2);
	--g-b1: var(--bg-u6);
	--g-b2: var(--bg-u6);
	--g-g1: var(--bg-u2);
	--g-g2: var(--bg-u5);
	--g-f-bg: var(--bg-u4);
	--g-f-b1: var(--bg-u5);
	--g-sh: rgba(0,0,0,0.07);

	--f-sh1: 0.3;
	--f-sh2: 0.5;
	--f-sh3: 0.02;

	--f-sel-sh: #e80;

	--fm-off: #c4a;
	--mp-sh: #bbb;
	--mp-b-bg: transparent;

	text-shadow: none;
}
html.a {
	--op-aa-sh: 0 0 .2em var(--bg-d3) inset;

	--btn-bs: 0 0 .2em var(--bg-d3);
}
html.az {
	--btn-1-bs: 0 0 .1em var(--fg) inset;
}
html.ay {
	--op-aa-sh: 0 .1em .2em #ccc;
	--op-aa-bg: var(--bg-max);
}
html.b {
	--btn-bs: 0 .05em 0 var(--bg-d3) inset;
	--btn-1-bs: 0 .05em 0 var(--btn-1h-bg) inset;

	--tree-bg: var(--bg);

	--g-bg: var(--bg);
	--g-b1: var(--bg);
	--g-b2: var(--bg);
	--g-g1: var(--bg);
	--g-sh: rgba(0,0,0,0);

	--op-aa-bg: rgba(255,255,255,0.06);

	--u2-sbtn-b1: #fc0;
	--u2-txt-bg: transparent;
	--u2-tab-1-sh: var(--bg);
	--u2-b1-bg: rgba(128,128,128,0.15);
	--u2-b2-bg: var(--u2-b1-bg);

	--f-sh1: 0.1;
	--mp-b-bg: transparent;
}
html.bz {
	--fg: #cce;
	--fg-weak: #bbd;

	--bg-u5: #3b3f58;
	--bg-u4: #1e2130;
	--bg-u3: #1e2130;
	--bg-u1: #1e2130;
	--bg: #11121d;
	--bg-d1: #232536;
	--bg-d2: #34384e;
	--bg-d3: #34384e;

	--row-alt: #181a27;

	--a-b: #fb4;

	--btn-bg: #202231;
	--btn-h-bg: #2d2f45;
	--btn-1-bg: #eb6;
	--btn-1-fg: #000;
	--btn-1h-fg: #000;
	--btn-1h-bg: #ff9;
	--txt-sh: a;

	--u2-tab-b1: var(--bg-u5);
	--u2-tab-1-fg: var(--fg-max);
	--u2-tab-1-bg: var(--bg);

	--srv-1: #79b;

	--g-sel-bg: #ba2959;
	--g-fsel-bg: #e6336e;

	--f-h-b1: #34384e;
	--mp-sh: #11121d;
	/*--mp-b-bg: #2c3044;*/
	--f-play-bg: var(--btn-1-bg);
}
html.by {
	--bg: #f2f2f2;

	--row-alt: #f9f9f9;

	--scroll: var(--a);

	--btn-1-bg: #07a;
	--btn-1h-bg: var(--a-hil);

	--op-aa-bg: #fff;

	--u2-sbtn-b1: #c70;
	--u2-tab-1-b1: #999;
	--u2-tab-1-b2: #aaa;
	--u2-b-fg: #444;
}
html.c {
	font-weight: bold;

	--fg: #fff;
	--fg-weak: #cef;
	--bg-u5: #409;
	--bg-u2: linear-gradient(-35deg, #fd7233, #cd27a0, #5d47a5 49.5%, #16e9fb 50%, #3b6cc8 50.4%, #0e51ac);
	--bg: #37235d;
	--bg-u3: #407;

	--a: #f9dc22;
	--a-gray: #0ae;

	--tab-alt: #6ef;
	--row-alt: #47237d;
	--scroll: #ff0;

	--btn-fg: #fff;
	--btn-bg: #9019bf;
	--btn-h-bg: #a039ff;
	--chk-fg: #d90;

	--op-aa-bg: #f9dd22;

	--srv-1: #ea0;
	--mp-b-bg: transparent;
}
html.cz {
	--bgg: var(--bg-u2);

	--sel-bg: var(--bg-u5);
	--sel-fg: var(--fg);

	--btn-bb: .2em solid #709;
	--btn-bs: 0 .1em .6em rgba(255,0,185,0.5);
	--btn-1-bb: .2em solid #e90;
	--btn-1-bs: 0 .1em .8em rgba(255,205,0,0.9);

	--srv-3: #fff;

	--u2-tab-b1: var(--bg-d3);
	--u2-tab-1-bg: a;
}
html.cy {
	--fg: #fff;
	--fg-weak: #fff;
	--bg: #ff0;
	--bg-u2: #f00;
	--bg-u3: #f00;
	--bg-u5: #999;
	--bg-d3: #f77;
	--bg-d2: #ff0;

	--sel-bg: #f77;

	--a: #fff;
	--a-hil: #fff;
	--a-h-bg: #000;

	--tab-alt: #f00;
	--row-alt: #fff;
	--scroll: #fff;

	--btn-bg: #000;
	--btn-fg: #ff0;
	--btn-h-fg: #fff;
	--btn-1-bg: #ff0;
	--btn-1-fg: #000;
	--btn-bs: 0 .25em 0 #f00;
	--chk-fg: #fd0;

	--txt-bg: #000;
	--srv-1: #f00;
	--srv-3: #fff;
	--op-aa-bg: #fff;

	--u2-b1-bg: #f00;
	--u2-b2-bg: #f00;

	--g-sel-fg: #fff;
	--g-sel-bg: #aaa;
	--g-fsel-bg: #aaa;
}
html.dz {
	--fg: #4d4;
	--fg-weak: #2a2;

	--bg-u6: #020;
	--bg-u5: #050;
	--bg-u4: #020;
	--bg-u3: #020;
	--bg-u2: #020;
	--bg-u1: #020;
	--bg: #010;
	--bg-d1: #000;
	--bg-d2: #020;
	--bg-d3: #000;

	--tab-alt: #6f6;
	--row-alt: #030;

	--scroll: #0f0;

	--a: #9f9;
	--a-b: #cfc;
	--a-hil: #cfc;
	--a-dark: #afa;
	--a-gray: #2a2;

	--btn-bg: rgba(64,128,64,0.15);
	--btn-h-bg: #050;
	--btn-1-fg: #000;
	--btn-1-bg: #4f4;
	--btn-1h-bg: #3f3;
	--btn-bs: 0 0 0 .1em #080 inset;
	--btn-1-bs: a;

	--u2-btn-b1: var(--fg-weak);
	--u2-sbtn-b1: var(--fg-weak);
	--u2-tab-b1: var(--fg-weak);
	--u2-tab-1-fg: #fff;
	--u2-tab-1-bg: linear-gradient(to bottom, #151, var(--bg) 80%);
	--u2-b1-bg: #3a3;
	--u2-b2-bg: #3a3;

	--sort-1: #fff;
	--sort-2: #3f3;

	--srv-1: #3e3;
	--srv-2: #1a1;
	--srv-3: #0f0;
	--srv-3b: #070;

	--tree-bg: #010;

	--g-sel-b1: #c37;
	--g-sel-sh: #b36;
	--g-fsel-b1: #d48;

	--f-h-b1: #3b3;

	text-shadow: none;
	font-family: 'scp', monospace, monospace;
	font-family: var(--font-mono), 'scp', monospace, monospace;
}
html.dy {
	--fg: #000;
	--fg-max: #000;
	--fg-weak: #000;

	--bg-d3: #fff;
	--bg-d2: #fff;
	--bg-d1: #fff;
	--bg: #fff;
	--bg-u1: #fff;
	--bg-u2: #fff;
	--bg-u3: #fff;
	--bg-u4: #fff;
	--bg-u5: #fff;
	--bg-u6: #fff;
	--bg-max: #fff;

	--tab-alt: #000;
	--row-alt: #eee;

	--scroll: #000;

	--a: #000;
	--a-b: #000;
	--a-hil: #000;
	--a-gray: #bbb;
	--a-dark: #000;

	--btn-fg: #000;
	--btn-h-fg: #000;
	--btn-h-bg: #fff;
	--btn-1-fg: #fff;
	--btn-1-bg: #000;
	--btn-1h-bg: #555;
	--chk-fg: a;
	--txt-sh: a;
	--txt-bg: a;

	--op-a-sh: a;

	--u2-txt-bg: a;
	--u2-tab-1-sh: a;
	--u2-tab-1-b1: a;
	--u2-tab-1-b2: a;
	--u2-tab-1-fg: a;
	--u2-tab-1-bg: a;
	--u2-b1-bg: #000;
	--u2-b2-bg: #000;

	--ud-b1: a;

	--sort-1: a;
	--sort-2: a;

	--srv-1: a;
	--srv-2: a;
	--srv-3: a;
	--srv-3b: a;

	--tree-bg: #fff;

	--g-sel-bg: #000;
	--g-fsel-bg: #444;
	--g-fsel-ts: #000;
	--g-fg: a;
	--g-bg: a;
	--g-b1: a;
	--g-b2: a;
	--g-g1: a;
	--g-g2: a;
	--g-f-bg: a;
	--g-f-b1: a;
	--g-sh: a;

	--f-sh1: a;
	--f-sh2: a;
	--f-sh3: a;

	--f-sel-sh: #000;

	--fm-off: a;
	--mp-sh: a;
	--mp-b-bg: #fff;
}
* {
	line-height: 1.2em;
}
::selection {
	color: var(--sel-fg);
	background: var(--sel-bg);
	text-shadow: none;
}
html,body,tr,th,td,#files,a,#blogout {
	color: inherit;
	background: none;
	font-weight: inherit;
	font-size: inherit;
	padding: 0;
	border: none;
}
html {
	color: var(--fg);
	background: var(--bgg);
	font-family: sans-serif;
	font-family: var(--font-main), sans-serif;
	text-shadow: 1px 1px 0px var(--bg-max);
}
html, body {
	margin: 0;
	padding: 0;
}
pre, code, tt, #doc, #doc>code {
	font-family: 'scp', monospace, monospace;
	font-family: var(--font-mono), 'scp', monospace, monospace;
}
.ayjump {
	position: fixed;
	overflow: hidden;
	width: 0;
	height: 0;
	color: var(--bg);
}
html .ayjump:focus {
	z-index: 80386;
	color: #fff;
	color: var(--a-hil);
	background: #069;
	background: var(--bg-u2);
	border: .2em solid var(--a);
	box-shadow: none;
	outline: none;
	width: auto;
	height: auto;
	top: .5em;
	left: .5em;
	padding: .5em .7em;
}
#path,
#path * {
	font-size: 1em;
}
#path {
	color: var(--fg);
	text-shadow: 1px 1px 0 var(--bg-max);
	font-weight: normal;
	display: inline-block;
	padding: .35em .5em .2em .5em;
	margin: 1.3em 0 -.2em 0;
	font-size: 1.4em;
}
html.y #path {
	text-shadow: none;
}
#path #entree {
	margin-left: -.7em;
}
#files {
	z-index: 1;
	top: -.3em;
	border-spacing: 0;
	position: relative;
}
#files tbody a {
	display: block;
	padding: .5em 0;
	margin: -.3em 0;
	scroll-margin-top: 45vh;
}
#files tr {
	scroll-margin-top: 25vh;
	scroll-margin-bottom: 20vh;
}
#files tbody div a {
	color: var(--tab-alt);
}
a, #blogout, #files tbody div a:last-child {
	color: var(--a);
	padding: .2em;
	text-decoration: none;
}
#blogout {
	margin: -.2em;
}
#blogout:hover,
a:hover {
	color: var(--a-hil);
	background: var(--a-h-bg);
}
#files a:hover {
	color: var(--fg-max);
	background: var(--bg-d3);
	text-decoration: underline;
}
#files thead th {
	position: sticky;
	top: -1px;
}
#files thead a {
	color: var(--f-gray);
	font-weight: normal;
}
.s0:after,
.s1:after {
	content: '⌄';
	margin-left: -.15em;
}
.s0r:after,
.s1r:after {
	content: '⌃';
	margin-left: -.15em;
}
.s0:after,
.s0r:after {
	color: var(--sort-1);
}
.s1:after,
.s1r:after {
	color: var(--sort-2);
}
#files thead th:after {
	margin-right: -.5em;
}
#files tbody tr:hover td,
#files tbody tr:hover td+td {
	background: var(--bg-d1);
	box-shadow: 0 1px 0 var(--bg-u5) inset, 0 -1px 0 var(--bg-u5) inset;
}
#files thead th {
	padding: .3em;
	background: var(--bg);
	border-bottom: 1px solid var(--f-h-b1);
	cursor: pointer;
}
html.y #files thead th {
	box-shadow: 0 1px 0 rgba(0,0,0,0.12);
}
html #files.hhpick thead th {
	color: #f7d;
	background: #000;
	box-shadow: .1em .2em 0 #f6c inset, -.1em -.1em 0 #f6c inset;
}
#files td {
	margin: 0;
	padding: .3em .5em;
	background: var(--bg);
	max-width: var(--file-td-w);
	word-wrap: break-word;
	overflow: hidden;
}
#files tr.fade a {
	color: #999;
	color: rgba(255, 255, 255, 0.4);
	font-style: italic;
}
html.y #files tr.fade a {
	color: #999;
	color: rgba(0, 0, 0, 0.4);
}
#files tr:nth-child(2n) td {
	background: var(--row-alt);
}
#files td+td {
	box-shadow: 1px 0 0 0 rgba(128,128,128,var(--f-sh1)) inset, 0 1px 0 rgba(255,255,255,var(--f-sh2)) inset, 0 -1px 0 rgba(255,255,255,var(--f-sh2)) inset;
}
#files tr:nth-child(2n+1) td+td {
	box-shadow: 1px 0 0 0 rgba(128,128,128,var(--f-sh1)) inset, 0 1px 0 rgba(0,0,0,var(--f-sh3)) inset, 0 -1px 0 rgba(0,0,0,var(--f-sh3)) inset;
}
#files td:first-child {
	border-radius: .25em 0 0 .25em;
	white-space: nowrap;
}
#files td:last-child {
	border-radius: 0 .25em .25em 0;
}
#files tbody td:nth-child(3) {
	font-family: 'scp', monospace, monospace;
	font-family: var(--font-mono), 'scp', monospace, monospace;
	text-align: right;
	padding-right: 1em;
	white-space: nowrap;
}
#files tbody td:first-child {
	color: var(--f-gray);
	text-align: center;
}
#files tbody tr td:last-child {
	white-space: nowrap;
}
#files thead th[style] {
	width: auto !important;
}
#files .srch_hdr a {
	display: inline;
}
#path a {
	padding: 0 .35em;
	position: relative;
	z-index: 1;
	/* ie: */
	border-bottom: .1em solid #777\9;
	margin-right: 1em\9;
}
#path a:first-child {
	padding-left: .8em;
}
#path i {
	width: 1.05em;
	height: 1.05em;
	margin: -.5em .15em -.15em -.7em;
	display: inline-block;
	border: 1px solid rgba(255,224,192,0.3);
	border-width: .05em .05em 0 0;
	transform: rotate(45deg);
	background: linear-gradient(45deg, rgba(0,0,0,0) 40%, rgba(0,0,0,0.25) 75%, rgba(0,0,0,0.35));
}
html.y #path i {
	background: none;
	border-color: rgba(0,0,0,0.2);
	border-width: .1em .1em 0 0;
}
#path a:hover {
	color: var(--fg-max);
	background: linear-gradient(90deg, rgba(0,0,0,0), rgba(0,0,0,0.2), rgba(0,0,0,0));
}
html.y #path a:hover {
	background: none;
}
.logue {
	padding: .2em 0;
	position: relative;
	z-index: 1;
}
.logue.hidden,
.logue:empty {
	display: none;
}
.logue.raw {
	white-space: pre;
	font-family: 'scp', 'consolas', monospace;
	font-family: var(--font-mono), 'scp', 'consolas', monospace;
}
#doc>iframe,
.logue>iframe {
	background: var(--bgg);
	border: 1px solid var(--bgg);
	border-width: 0 .3em 0 .3em;
	border-radius: .5em;
	visibility: hidden;
	margin: 0 -.3em;
	width: 100%;
	height: 0;
}
#doc>iframe.focus,
.logue>iframe.focus {
	box-shadow: 0 0 .1em .1em var(--a);
}
#pro.logue>iframe {
	height: 100vh;
}
#pro.logue {
	margin-bottom: .8em;
}
#epi.logue {
	margin: .8em 0;
}
#epi.logue.mdo:before {
	content: 'README.md';
	text-align: center;
	display: block;
	margin-top: -1.5em;
}
#epi.logue.mdo {
	border-top: 1px solid var(--bg-u5);
	margin-top: 2.5em;
}
.mdo>h1:first-child,
.mdo>h2:first-child,
.mdo>h3:first-child {
	margin-top: 1.5rem;
}
.mdo {
	max-width: 52em;
}
.mdo.sb,
.logue.mdo>iframe {
	max-width: 54em;
}
.mdo,
.mdo * {
	line-height: 1.5em;
}
#srv_info,
#srv_info2,
#acc_info {
	color: var(--srv-2);
	background: var(--bg);
	white-space: nowrap;
}
#srv_info,
#acc_info {
	position: absolute;
	font-size: .8em;
	top: .5em;
}
#srv_info {
	left: 2em;
	padding-right: .5em;
}
#acc_info,
#srv_info span,
#srv_info2 span {
	color: var(--srv-1);
}
#srv_info2 {
	display: none;
}
#acc_info {
	right: 2em;
}
#acc_info > span:not([id]) {
	color: var(--srv-1);
	margin-right: .6em;
}
#acc_info span.warn {
	color: var(--srv-3);
	border-bottom: 1px solid var(--srv-3b);
}
#flogout {
	display: inline;
}
#goh+span {
	color: var(--bg-u5);
	padding-left: .5em;
	margin-left: .5em;
	border-left: .2em solid var(--bg-u5);
}
#repl {
	padding: .33em;
}
#files a.doc {
	color: var(--a-gray);
}
#files a.doc.bri {
	color: var(--tab-alt);
}
#files a.play {
	color: var(--a-dark);
	padding: .3em;
	margin: -.3em;
}
#files tbody tr.play td,
#files tbody tr.play td+td,
#files tbody tr.play div a {
	background: #fc0;
	background: var(--f-play-bg);
	color: var(--f-play-fg);
	text-shadow: none;
}
#files tbody tr.play a {
	color: inherit;
}
#files tbody tr.play a:hover {
	color: var(--btn-1h-fg);
	background: var(--btn-1h-bg);
	box-shadow: var(--btn-1h-bs);
	border-bottom: var(--btn-1h-bb);
}
#ggrid {
	margin: -.2em -.5em;
}
#ggrid>a>span {
	overflow: hidden;
	display: block;
	display: -webkit-box;
	line-clamp: var(--grid-ln);
	-webkit-line-clamp: var(--grid-ln);
	-webkit-box-orient: vertical;
	padding-top: .3em;
}
#ggrid>a {
	display: inline-block;
	width: 10em;
	width: var(--grid-sz);
	vertical-align: top;
	overflow-wrap: break-word;
	border-radius: .3em;
	padding: .3em;
	margin: .5em;
	color: var(--g-fg);
	background: var(--g-bg);
	border: 1px solid var(--g-b1);
	border-top: 1px solid var(--g-b2);
	box-shadow: 0 .1em .3em var(--g-sh);
}
#ggrid>a:focus,
#ggrid>a:hover {
	color: var(--g-f-fg);
	background: var(--g-f-bg);
	border-color: var(--g-f-b1);
	box-shadow: 0 .1em .3em var(--g-sh);
}
#ggrid>a img {
	border-radius: .2em;
	max-width: 10em;
	max-width: var(--grid-sz);
	max-height: 8em;
	max-height: calc(var(--grid-sz)/1.25);
	margin: 0 auto;
	display: block;
}
#ggrid.nocrop>a img {
	max-height: 20em;
	max-height: calc(var(--grid-sz)*2);
}
#ggrid>a.dir:before {
	content: '📂';
}
#ggrid>a.dir>span {
	color: var(--g-dfg);
}
#ggrid>a.au:before {
	content: '▶';
}
#ggrid>a:before {
	display: block;
	position: absolute;
	padding: .3em 0;
	margin: -.4em;
	text-shadow: 0 0 .1em var(--bg-max);
	background: linear-gradient(135deg,rgba(255,255,255,0) 50%,rgba(255,255,255,0.2));
	border-radius: .3em;
	font-size: 2em;
	transition: font-size .15s, margin .15s;
}
#ggrid>a:focus:before,
#ggrid>a:hover:before {
	font-size: 2.5em;
	margin: -.2em;
}
#ggrid>a[tt] {
	background: linear-gradient(135deg, var(--g-g1) 95%, var(--g-g2) 95%);
}
#ggrid>a[tt]:focus,
#ggrid>a[tt]:hover {
	background: var(--g-f-bg);
}
#ggrid>a.play,
#ggrid>a[tt].play {
	color: var(--g-sel-fg);
	background: #fc0;
	background: var(--g-play-bg);
	border-color: var(--g-play-b1);
	border-top: 1px solid var(--g-play-b2);
	box-shadow: 0 .1em 1.2em var(--g-play-sh);
}
#files tbody tr.sel td,
#ggrid>a.sel,
#ggrid>a[tt].sel {
	color: var(--g-sel-fg);
	background: #f3c;
	background: var(--g-sel-bg);
	border-color: var(--g-sel-b1);
}
#ggrid>a.sel>span {
	color: var(--g-sel-fg);
}
#ggrid>a.sel,
#ggrid>a[tt].sel {
	border-top: 1px solid var(--g-fsel-b1);
	box-shadow: 0 .1em 1.2em var(--g-sel-sh);
	transition: all 0.2s cubic-bezier(.2, 2.2, .5, 1); /* https://cubic-bezier.com/#.4,2,.7,1 */
}
#files tbody tr.sel:hover td,
#files tbody tr.sel:focus td,
#ggrid>a.sel:hover,
#ggrid>a.sel:focus {
	color: var(--g-sel-fg);
	background: var(--g-fsel-bg);
	border-color: var(--g-fsel-b1);
	text-shadow: 1px 1px 0 var(--g-fsel-ts);
}
#ggrid>a.sel img,
#ggrid>a.play img {
	opacity: .7;
	filter: contrast(130%) brightness(107%);
}
#ggrid>a.sel img {
	box-shadow: 0 0 1em var(--g-sel-sh);
}
#ggrid>a.play img {
	box-shadow: 0 0 1em var(--g-play-sh);
}
#ggrid a {
	scroll-margin-top: 25vh;
	scroll-margin-bottom: 20vh;
}
#files tr.sel a,
#files tr.sel a.play {
	color: var(--fg2-max);
}
#files tr.sel a:hover {
	color: var(--fg-max);
	text-shadow: none;
}
#files tr.sel a.play.act {
	text-shadow: 0 0 1px var(--fg2-max);
}
#files tr:focus {
	outline: none;
	position: relative;
}
#files tr:focus td+td {
	background: var(--bg-d3);
	box-shadow: 0 .2em 0 var(--f-sel-sh), 0 -.2em 0 var(--f-sel-sh);
}
#files tr:focus:not(.play):not(.sel) td:first-child {
	background: var(--bg-d3);
	box-shadow: -.2em .2em 0 var(--f-sel-sh), -.2em -.2em 0 var(--f-sel-sh);
}
#player {
	display: none;
}
#widget {
	position: fixed;
	font-size: 1.4em;
	left: 0;
	right: 0;
	bottom: -6em;
	height: 6em;
	width: 100%;
	z-index: 3;
	touch-action: none;
}
#widget.anim {
	transition: bottom 0.15s;
}
#widget.open {
	box-shadow: 0 0 1em rgba(0,48,64,0.2);
	bottom: 0;
}
html.y #widget.open {
	border-top: .2em solid var(--bg-u2);
}
#widgeti {
	position: relative;
	z-index: 10;
	width: 100%;
	height: 100%;
}
#fshr,
#wtgrid,
#wtico {
	position: relative;
	font-size: .9em;
	top: -.04em;
}
#wtgrid {
	font-size: .75em;
	padding: .1em;
	top: -.12em;
}
#wtico {
	cursor: pointer;
}
@keyframes spin {
	100% {transform: rotate(360deg)}
}
@keyframes fadein {
	0% {opacity: 0}
	100% {opacity: 1}
}
#wtoggle {
	position: absolute;
	white-space: nowrap;
	top: -1em;
	right: 0;
	height: 1em;
	font-size: 2em;
	line-height: 1em;
	text-align: center;
	text-shadow: none;
	box-shadow: 0 0 .5em var(--mp-sh);
	border-radius: .3em 0 0 0;
	padding: 0 0 0 .1em;
	color: var(--fg-max);
}
#wtoggle,
#widgeti {
	background: #fff;
	background: var(--bg-u3);
}
#wfs, #wfm, #wzip, #wnp, #wm3u {
	display: none;
}
#wfs, #wzip, #wnp, #wm3u {
	margin-right: .2em;
	padding-right: .2em;
	border: 1px solid var(--bg-u5);
	border-width: 0 .1em 0 0;
}
#wfm.act+#wzip1+#wzip,
#wfm.act+#wzip1+#wzip+#wnp {
	margin-left: .2em;
	padding-left: .2em;
	border-left-width: .1em;
}
#wfs.act,
#wfm.act {
	display: inline-block;
}
#wtoggle,
#wtoggle * {
	line-height: 1em;
}
#wtoggle.sel #wzip,
#wtoggle.m3u #wm3u,
#wtoggle.np #wnp {
	display: inline-block;
}
#wtoggle.sel #wzip1,
#wtoggle.sel.np #wnp {
	display: none;
}
#wfm a,
#wnp a,
#wm3u a,
#zip1,
#wzip a {
	font-size: .5em;
	padding: 0 .3em;
	margin: -.3em .1em;
	position: relative;
	display: inline-block;
}
#zip1 {
	font-size: .38em;
}
#wm3u a {
	margin: -.2em .1em;
	font-size: .45em;
}
#wfs {
	font-size: .36em;
	text-align: right;
	line-height: 1.3em;
	padding: 0 .3em 0 0;
	border-width: 0 .25em 0 0;
}
#wfm span,
#wm3u span,
#zip1 span,
#wnp span {
	font-size: .6em;
	display: block;
}
#zip1 span {
	font-size: .9em;
}
#wnp span {
	font-size: .7em;
}
#wm3u span {
	font-size: .77em;
	padding-top: .2em;
}
#wfm a:not(.en) {
	opacity: .3;
	color: var(--fm-off);
}
#wfm a.hide {
	display: none;
}
#files tbody tr.fcut td,
#ggrid>a.fcut {
	animation: fcut .5s ease-out;
}
@keyframes fcut {
	0% {opacity:0}
	100% {opacity:1}
}
#ggrid>a.glow {
	animation: gexit .6s ease-out;
}
@keyframes gexit {
	0% {box-shadow: 0 0 0 2em var(--a)}
	100% {box-shadow: 0 0 0em 0em var(--a)}
}
#wzip a {
	font-size: .4em;
	margin: -.3em .1em;
}
#wtoggle.sel .l1 {
	top: -.6em;
	padding: .4em .3em;
}
#barpos,
#barbuf {
	position: absolute;
	bottom: 1em;
	left: 1em;
	height: 2em;
	border-radius: 9em;
	width: calc(100% - 2em);
}
#barbuf {
	background: var(--mp-b-bg);
	z-index: 21;
}
#barpos {
	box-shadow: -.03em -.03em .7em rgba(0,0,0,0.5) inset;
	z-index: 22;
}
#pctl {
	position: absolute;
	top: .5em;
	left: 1em;
}
#pctl a {
	display: inline-block;
	font-size: 1.25em;
	width: 1.3em;
	height: 1.2em;
	text-align: center;
	border-radius: .3em;
}
#pvol {
	position: absolute;
	top: .7em;
	right: 1em;
	height: 1.6em;
	border-radius: 9em;
	max-width: 12em;
	width: calc(100% - 10.5em);
	background: rgba(0,0,0,0.2);
}
#widget.cmp {
	height: 1.6em;
	bottom: -1.6em;
}
#widget.cmp.open {
	bottom: 0;
}
#widget.cmp #wtoggle {
	font-size: 1.2em;
}
#widget.cmp #fshr,
#widget.cmp #wtgrid {
	display: none;
}
#widget.cmp #pctl {
	top: 0;
	left: 0;
	font-size: .75em;
}
#widget.cmp #pctl a {
	margin: 0;
}
#widget.cmp #barpos,
#widget.cmp #barbuf {
	height: 1.6em;
	width: calc(100% - 11em);
	border-radius: 0;
	left: 5em;
	top: 0;
}
#widget.cmp #pvol {
	top: 0;
	right: 0;
	max-width: 5.8em;
	border-radius: 0;
}
.opview {
	display: none;
}
.opview.act {
	display: block;
}
#ops a {
	color: var(--a);
	text-shadow: 1px 1px 1px var(--op-a-sh);
	font-size: 1.5em;
	padding: .25em .4em;
	margin: 0;
}
#ops a.act {
	color: #fff;
	color: var(--op-aa-fg);
	background: #000;
	background: var(--op-aa-bg);
	border-radius: 0 0 .2em .2em;
	border-bottom: .3em solid var(--a-b);
	box-shadow: var(--op-aa-sh);
}
#ops a svg {
	width: 1.75em;
	height: 1.75em;
	margin: -.5em -.3em;
}
html.y #ops svg circle {
	stroke: black;
}
#ops {
	padding: .3em .6em;
	white-space: nowrap;
}
#noie {
	color: #b60;
	margin: 0 0 0 .5em;
}
.opbox {
	padding: .5em;
	border-radius: 0 .3em .3em 0;
	border-width: 1px 1px 1px 0;
	max-width: 41em;
	max-width: min(41em, calc(100% - 2.6em));
}
.opbox input {
	position: relative;
	margin: .5em;
}
#op_cfg input[type=text] {
	top: -.3em;
}
.opview input[type=text] {
	color: var(--fg);
	background: var(--txt-bg);
	border: none;
	box-shadow: 0 0 2px var(--txt-sh);
	border-bottom: 1px solid #999;
	border-color: var(--a);
	border-radius: .2em;
	padding: .2em .3em;
}
.opview input.err {
	color: var(--err-fg);
	background: var(--err-bg);
	border-color: var(--err-b1);
	box-shadow: 0 0 .7em var(--err-b1);
	text-shadow: 1px 1px 0 var(--err-ts);
	outline: none;
}
input[type="checkbox"]+label {
	color: var(--chk-fg);
}
input[type="radio"]:checked+label,
input[type="checkbox"]:checked+label {
	color: #0e0;
	color: var(--btn-1-bg);
}
input[type="checkbox"]:checked+label {
	box-shadow: var(--btn-1-bs);
	border-bottom: var(--btn-1-bb);
}
html.dz input {
	font-family: 'scp', monospace, monospace;
	font-family: var(--font-mono), 'scp', monospace, monospace;
}
.opwide div>span>input+label {
	padding: .3em 0 .3em .3em;
	margin: 0 0 0 -.3em;
	cursor: pointer;
}
.opview input.i {
	width: calc(100% - 16.2em);
}
input.drc_v,
input.eq_gain {
	width: 3em;
	text-align: center;
	margin: 0 .6em;
}
#audio_drc table,
#audio_eq table {
	border-collapse: collapse;
}
#audio_drc td,
#audio_eq td {
	text-align: center;
}
#audio_eq a.eq_step {
	font-size: 1.5em;
	display: block;
	padding: 0;
}
#au_drc,
#au_eq {
	display: block;
	margin-top: .5em;
	padding: 1.3em .3em;
}
#au_drc {
	padding: .4em .3em;
}
#ico1 {
	cursor: pointer;
}



#srch_form {
	padding: 0 .5em .5em 0;
}
#srch_form table {
	display: inline-block;
}
#srch_form td {
	padding: .6em .6em;
}
#srch_form td:first-child {
	width: 3em;
	padding-right: .2em;
	text-align: right;
}
#srch_form:not(.tags) #tsrch_tags,
#srch_form:not(.tags) #tsrch_adv {
	display: none;
}
#op_search input {
	margin: .1em 0 0 0;
}
#srch_q {
	white-space: pre;
	color: var(--a-b);
	min-height: 1em;
	margin: .2em 0 -1em 1.6em;
}
#srch_q.err {
	color: var(--srv-3);
}
#tq_raw {
	width: calc(100% - 2em);
	margin: .3em 0 0 1.4em;
}
@media (max-width: 130em) { #srch_form.tags #tq_raw { width: calc(100% - 34em) } }
@media (max-width: 95em) { #srch_form.tags #tq_raw { width: calc(100% - 2em) } }
#tq_raw td+td {
	width: 100%;
}
#op_search #q_raw {
	width: 100%;
	display: block;
}
#files td div span {
	color: var(--fg-max);
	padding: 0 .4em;
	font-weight: bold;
	font-style: italic;
}
#files td div a:hover {
	background: var(--bg-u5);
	color: var(--fg-max);
}
#files td div a {
	display: inline-block;
	white-space: nowrap;
}
#files td div a:last-child {
	width: 100%;
}
#files td div {
	border-collapse: collapse;
	width: 100%;
}
#wrap {
	margin: 1.8em 1.5em 0 1.5em;
	min-height: 70vh;
	padding-bottom: 7em;
}
#tree {
	display: none;
	position: absolute;
	left: 0;
	bottom: 0;
	top: 7em;
	overflow-x: hidden;
	overflow-y: auto;
	-ms-scroll-chaining: none;
	overscroll-behavior-y: none;
	box-shadow: 0 0 1em var(--bg-d2), 0 -1px 0 rgba(128,128,128,0.3);
}
#tree,
html {
	scrollbar-color: var(--scroll) var(--bg-u3);
}
#treeh {
	position: sticky;
	z-index: 1;
	top: 0;
	height: 2.2em;
	line-height: 2.2em;
	background: var(--tree-bg);
	border-bottom: 1px solid var(--bg-d3);
	overflow: hidden;
}
#treepar {
	z-index: 1;
	position: fixed;
	background: var(--tree-bg);
	left: -.96em;
	width: calc(.3em + var(--nav-sz) - var(--sbw));
	border-bottom: 1px solid var(--bg-u5);
	overflow: hidden;
}
#treepar.off {
	display: none;
}
.np_open #thx_ff {
	padding: 4.5em 0;
	/* widget */
}
#tree::-webkit-scrollbar-track,
#tree::-webkit-scrollbar {
	background: var(--bg-u3);
}
#tree::-webkit-scrollbar-thumb {
	background: var(--scroll);
}
#tree:hover {
	z-index: 2;
}
#treeul {
	position: relative;
	left: -2.2em;
	width: calc(100% + 2em);
}
.btn {
	color: var(--btn-fg);
	background: #eee;
	background: var(--btn-bg);
	box-shadow: var(--btn-bs);
	border-bottom: var(--btn-bb);
	border-radius: .3em;
	padding: .2em .4em;
	font-size: 1.2em;
	margin: .2em;
	display: inline-block;
	white-space: pre;
	position: relative;
	top: -.12em;
}
html.c .btn,
html.a .btn {
	border-radius: .2em;
}
html.dz .btn {
	font-size: 1em;
}
.btn:hover {
	color: var(--btn-h-fg);
	background: var(--btn-h-bg);
	box-shadow: var(--btn-h-bs);
	border-bottom: var(--btn-h-bb);
}
.tgl.btn.on {
	background: #000;
	background: var(--btn-1-bg);
	color: #fff;
	color: var(--btn-1-fg);
	text-shadow: none;
	box-shadow: var(--btn-1-bs);
	border-bottom: var(--btn-1-bb);
}
.tgl.btn.on:hover {
	color: var(--btn-1h-fg);
	background: var(--btn-1h-bg);
	box-shadow: var(--btn-1h-bs);
	border-bottom: var(--btn-1h-bb);
}
#detree {
	padding: .3em .5em;
	font-size: 1.5em;
	line-height: 1.5em;
}
#tree ul,
#tree li {
	padding: 0;
	margin: 0;
}
#tree ul {
	border-left: .2em solid var(--bg-u5);
	margin-left: .44em;
}
#tree li {
	margin-left: .6em;
	list-style: none;
	border-top: 1px solid var(--bg-u5);
}
#tree li.offline>a:first-child:before {
	content: '❌';
	position: absolute;
	margin-left: -.25em;
	z-index: 3;
}
#tree ul a.sel {
	background: #000;
	background: var(--bg-d3);
	box-shadow: -.8em 0 0 var(--g-sel-b1) inset;
	color: #fff;
	color: var(--fg-max);
}
#tree ul a.hl {
	color: #fff;
	color: var(--btn-1-fg);
	background: #000;
	background: var(--btn-1-bg);
	text-shadow: none;
}
#tree ul a.ld::before {
	font-weight: bold;
	font-family: sans-serif;
	display: inline-block;
	text-align: center;
	width: 1em;
	margin: 0 .3em 0 -1.3em;
	color: var(--fg-max);
	opacity: 0;
	content: '◠';
	animation: .5s linear infinite forwards spin, ease .25s 1 forwards fadein;
}
#tree ul a.par {
	color: var(--fg-max);
}
#tree ul a {
	border-radius: .3em;
	display: inline-block;
}
.ntree a+a {
	width: calc(100% - 2.2em);
	line-height: 1em;
}
#tree.nowrap li {
	min-height: 1.4em;
	white-space: nowrap;
}
#tree.nowrap .ntree a+a:hover {
	background: rgba(16, 16, 16, 0.67);
	min-width: calc(var(--nav-sz) - 2em);
	width: auto;
}
html.y #tree.nowrap .ntree a+a:hover {
	background: rgba(255, 255, 255, 0.67);
	color: var(--fg-max);
}
#docul a:hover,
#tree .ntree a+a:hover {
	background: var(--bg-d2);
	color: var(--fg-max);
}
.ntree a:first-child {
	font-family: 'scp', monospace, monospace;
	font-family: var(--font-mono), 'scp', monospace, monospace;
	font-size: 1.2em;
	line-height: 0;
}
.dumb_loader_thing {
	display: block;
	margin: 1em .3em 1em 1em;
	padding: 0 1.2em 0 0;
	font-size: 4em;
	min-width: 1em;
	min-height: 1em;
	opacity: 0;
	animation: 1s linear .15s infinite forwards spin, .2s ease .15s 1 forwards fadein;
	position: fixed;
	top: .3em;
	z-index: 9;
}
#dlt_t {
	left: 0;
}
#dlt_f {
	right: .5em;
}
#files .cfg {
	display: none;
	font-size: 2em;
	white-space: nowrap;
}
#files th:hover .cfg {
	display: block;
	width: 1em;
	border-radius: .2em;
	margin: -1.2em auto 0 auto;
	background: var(--bg-u5);
}
#files th span {
	position: relative;
	white-space: nowrap;
}
#files>thead>tr>th.min,
#files td.min {
	display: none;
}
#files td:nth-child(2n) {
	color: var(--tab-alt);
}
#plazy {
	width: 1px;
	height: 1px;
	overflow: hidden;
	white-space: nowrap;
}
#blazy {
	text-align: center;
	font-size: 1.2em;
	margin: 1em 0;
}
#blazy code,
#blazy a {
	font-size: 1.1em;
	padding: 0 .2em;
}
.opwide,
#op_unpost,
#srch_form {
	max-width: none;
	margin-right: 1.5em;
}
.opwide>div {
	display: inline-block;
	vertical-align: top;
	border-left: .4em solid var(--bg-u5);
	margin: .7em 0 .7em .5em;
	padding-left: .5em;
}
.opwide>div>h3 {
	color: var(--fg-weak);
	margin: 0 .4em;
	padding: 0;
}
#op_cfg>div>div>span {
	display: inline-block;
	padding: .2em .4em;
}
.opbox h3 {
	margin: .8em 0 0 .6em;
	padding: 0;
}
#thumbs,
#au_prescan,
#au_fullpre,
#au_os_seek,
#au_osd_cv,
#u2tdate {
	opacity: .3;
}
#griden.on+#thumbs,
#au_preload.on+#au_prescan,
#au_preload.on+#au_prescan+#au_fullpre,
#au_os_ctl.on+#au_os_seek,
#au_os_ctl.on+#au_os_seek+#au_osd_cv,
#u2turbo.on+#u2tdate {
	opacity: 1;
}
#wraptree.on+#hovertree {
	display: none;
}
.ghead {
	background: #fff;
	background: var(--bg-u2);
	border-radius: .3em;
	padding: .2em .5em;
	line-height: 2.3em;
	margin-bottom: 1.5em;
}
#hdoc,
#ghead {
	position: sticky;
	top: -.3em;
	z-index: 2;
}
.ghead .btn {
	position: relative;
	top: 0;
}
.ghead>span {
	white-space: pre;
	padding-left: .3em;
}
#tailbtns {
	display: none;
}
#taildoc.on+#tailbtns {
	display: inherit;
	display: unset;
}
#op_unpost {
	padding: 1em;
}
#op_unpost td {
	padding: .2em .4em;
}
#op_unpost a {
	margin: 0;
	padding: 0;
}
#unpost td:nth-child(3),
#unpost td:nth-child(4) {
	text-align: right;
}
#shui,
#rui {
	background: #fff;
	background: var(--bg);
	position: fixed;
	top: 0;
	left: 0;
	width: calc(100% - 2em);
	height: auto;
	overflow: auto;
	max-height: calc(100% - 2em);
	border-bottom: .5em solid var(--f-gray);
	box-shadow: 0 0 5em rgba(0,0,0,0.8);
	padding: 1em;
	z-index: 765;
}
#shui div+div,
#rui div+div {
	margin-top: 1em;
}
#shui table,
#rui table {
	width: 100%;
	border-collapse: collapse;
}
#shui button {
	margin: 0 1em 0 0;
}
#shui .btn {
	font-size: 1em;
}
#shui td {
	padding: .8em 0;
}
#shui td+td,
#rui td+td {
	padding: .2em 0 .2em .5em;
}
#rn_vadv input {
	font-family: 'scp', monospace, monospace;
	font-family: var(--font-mono), 'scp', monospace, monospace;
}
#shui td+td,
#rui td+td,
#shui td input[type="text"],
#rui td input[type="text"] {
	width: 100%;
}
#shui td.exs input[type="text"] {
	width: 3em;
}
#rn_f.m td:first-child {
	white-space: nowrap;
}
#rn_f.m td+td {
	width: 50%;
}
#rn_f .err td,
#rn_f .err input[readonly],
#rui .ng input[readonly] {
	color: var(--err-fg);
	background: var(--err-bg);
}
#rui input[readonly] {
	color: var(--fg-max);
	background: var(--bg-u5);
	border: 1px solid rgba(255,255,255,0.2);
	padding: .2em .25em;
}
#rui h1 {
	margin: 0 0 .3em 0;
	padding: 0;
	font-size: 1.5em;
}
#doc {
	overflow: visible;
	background: #fff;
	background: var(--bg);
	margin: -1em 0 .5em 0;
	padding: 1em 0 1em 0;
	border-radius: .3em;
}
#doc.wrap {
	white-space: pre-wrap;
}
html.y #doc {
	box-shadow: 0 0 .3em var(--bg-u5);
	background: #f7f7f7;
}
#docul {
	position: relative;
}
#docul li.bn {
	text-align: center;
	padding: .5em;
}
#docul li.bn span {
	font-weight: bold;
	color: var(--fg-max);
}
#doc.prism {
	padding-left: 3em;
}
#doc>code {
	background: none;
	box-shadow: none;
	z-index: 1;
}
#doc.mdo {
	white-space: normal;
	font-family: sans-serif;
	font-family: var(--font-main), sans-serif;
}
#doc.prism * {
	line-height: 1.5em;
}
#doc .line-highlight {
	border-radius: .3em;
	box-shadow: 0 0 .5em rgba(128,128,128,0.2);
	background: linear-gradient(90deg, var(--bg-d3), var(--bg));
}
html.y #doc .line-highlight {
	background: linear-gradient(90deg, var(--bg-max), var(--bg));
}
#docul li {
	margin: 0;
}
#tree #docul li+li a {
	display: block;
}
#seldoc.sel {
	color: var(--fg2-max);
	background: var(--g-sel-b1);
}
#pvol,
#barbuf,
#barpos,
a.btn,
#u2btn,
#u2conf label,
#rui label,
#modal-ok,
#modal-ng,
#ops,
#ico1 {
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
#hkhelp {
	background: var(--bg);
}
#hkhelp table {
	margin: 2em 2em 0 2em;
	float: left;
}
#hkhelp th {
	border-bottom: 1px solid var(--bg-u5);
	background: var(--bg-u1);
	font-weight: bold;
	text-align: right;
}
#hkhelp tr+tr th {
	border-top: 1.5em solid var(--bg);
}
#hkhelp td {
	padding: .2em .3em;
}
#hkhelp td:first-child {
	font-family: 'scp', monospace, monospace;
	font-family: var(--font-mono), 'scp', monospace, monospace;
}
#hkhelp b {
	text-shadow: 1px 0 0 var(--fg), -1px 0 0 var(--fg), 0 -1px 0 var(--fg);
}
html.noscroll,
html.noscroll .sbar {
	scrollbar-width: none;
}
html.noscroll::-webkit-scrollbar,
html.noscroll .sbar::-webkit-scrollbar {
	display: none;
}




































/* bbox */

#bbox-overlay {
	display: none;
	opacity: 0;
	position: fixed;
	overflow: hidden;
	touch-action: pinch-zoom;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 10;
	background: rgba(0, 0, 0, 0.8);
	transition: opacity .3s ease;
}
#bbox-overlay.visible {
	opacity: 1;
}
.full-image {
	display: inline-block;
	position: relative;
	width: 100%;
	height: 100%;
	text-align: center;
}
.full-image figure {
	display: inline;
	margin: 0;
	height: 100%;
}
.full-image img,
.full-image video {
	display: inline-block;
	outline: none;
	width: auto;
	height: auto;
	max-width: 100%;
	max-height: 100%;
	max-height: calc(100% - 1.4em);
	margin-bottom: 1.4em;
	vertical-align: middle;
	transition: transform .23s, left .23s, top .23s, width .23s, height .23s;
}
.full-image img.nt,
.full-image video.nt {
	transition: none;
}
.full-image.vis img,
.full-image.vis video {
	box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
}
.full-image video {
	background: #222;
}
.full-image figcaption {
	display: block;
	position: fixed;
	bottom: .1em;
	width: 100%;
	text-align: center;
	white-space: normal;
	color: var(--fg);
	z-index: 1;
}
#bbox-overlay figcaption a {
	background: rgba(0, 0, 0, 0.6);
	border-radius: .4em;
	padding: .3em .6em;
}
html.y #bbox-overlay figcaption a {
	color: #0bf;
}
.full-image:before {
	content: "";
	display: inline-block;
	height: 50%;
	width: 1px;
	margin-right: -1px;
}
#bbox-slider {
	position: fixed;
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
	white-space: nowrap;
	transition: left .2s ease, transform .2s ease;
}
.bounce-from-right {
	animation: bounceFromRight .3s ease-out;
}
.bounce-from-left {
	animation: bounceFromLeft .3s ease-out;
}
.eog {
	animation: eog .2s;
}
@keyframes bounceFromRight {
	0% {margin-left: 0}
	50% {margin-left: -30px}
	100% {margin-left: 0}
}
@keyframes bounceFromLeft {
	0% {margin-left: 0}
	50% {margin-left: 30px}
	100% {margin-left: 0}
}
@keyframes eog {
	0% {filter: brightness(1.5)}
}
#bbox-next,
#bbox-prev {
	top: 50%;
	top: calc(50% - 30px);
	width: 44px;
	height: 60px;
	transition: background-color .3s ease, color .3s ease, left .3s ease, right .3s ease;
}
#bbox-btns button {
	transition: background-color .3s ease, color .3s ease;
}
#bbox-btns {
	transition: top .3s ease;
}
.bbox-btn {
	position: fixed;
}
#bbox-next.off {
	right: -2.6em;
}
#bbox-prev.off {
	left: -2.6em;
}
#bbox-btns.off {
	top: -2.2em;
}
#bbox-overlay button {
	cursor: pointer;
	outline: none;
	padding: 0 .3em;
	margin: 0 .4em;
	border: 0;
	border-radius: 15%;
	background: rgba(50, 50, 50, 0.5);
	color: rgba(255,255,255,0.7);
	font-size: 1.4em;
	line-height: 1.4em;
	vertical-align: top;
	font-variant: small-caps;
}
#bbox-overlay button:focus,
#bbox-overlay button:hover {
	color: rgba(255,255,255,0.9);
	background: rgba(50, 50, 50, 0.9);
}
#bbox-next {
	right: 1%;
}
#bbox-prev {
	left: 1%;
}
#bbox-btns {
	top: .5em;
	right: 2%;
	position: fixed;
}
#bbox-halp {
	color: var(--fg-max);
	background: #fff;
	background: var(--bg);
	position: absolute;
	top: 0;
	left: 0;
	z-index: 20;
	padding: .4em;
}
#bbox-halp td {
	padding: .2em .5em;
}
#bbox-halp td:first-child:not([colspan]) {
	text-align: right;
}
.bbox-spinner {
	width: 40px;
	height: 40px;
	display: inline-block;
	position: absolute;
	top: 50%;
	left: 50%;
	margin-top: -20px;
	margin-left: -20px;
}
.bbox-double-bounce1,
.bbox-double-bounce2 {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background-color: #fff;
	opacity: .6;
	position: absolute;
	top: 0;
	left: 0;
	animation: bounce 2s infinite ease-in-out;
}
.bbox-double-bounce2 {
	animation-delay: -1s;
}
@keyframes bounce {
	0%, 100% {transform: scale(0)}
	50% {transform: scale(1)}
}

























/* upload.css */

#op_up2k {
	padding: 0 1em 1em 1em;
}
#drops {
	display: none;
	z-index: 3;
	background: rgba(48, 48, 48, 0.7);
}
#drops.vis,
.dropzone {
	display: block;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}
.dropdesc {
	position: fixed;
	display: table;
	left: 10%;
	width: 78%;
	height: 26%;
	margin: 0;
	font-size: 3em;
	font-weight: bold;
	text-shadow: .05em .05em .1em #333;
	background: rgba(224, 224, 224, 0.2);
	box-shadow: 0 0 0 #999;
	border: .5em solid var(--ud-b1);
	border-radius: .5em;
	border-width: 1vw;
	color: #fff;
	transition: all 0.12s;
}
.dropdesc.hl.ok {
	border-color: #fff;
	box-shadow: 0 0 1em .4em #cf5, 0 0 1em #000 inset;
	background: rgba(24, 24, 24, 0.7);
	left: 8%;
	width: 82%;
	height: 32%;
	margin: -3vh 0;
}
.dropdesc.hl.err {
	background: rgba(224, 32, 65, 0.2);
	box-shadow: 0 0 1em .4em #f26;
	border-color: #fab;
}
.dropdesc>div {
	display: table-cell;
	vertical-align: middle;
	text-align: center;
}
.dropdesc>div>div {
	position: absolute;
	top: 40%;
	top: calc(50% - .5em);
	top: calc(50% - 1.2vw);
	left: -.8em;
	transition: top 0.12s;
}
.dropdesc>div>div+div {
	left: auto;
	right: -.8em;
}
.dropdesc b {
	font-size: .5em;
	font-size: 2vw;
	margin: 0 .8em;
	margin: 0 1.25vw;
	transition: font-size 0.12s;
}
.dropdesc.hl.ok b {
	border-bottom: .1em solid #fff;
	font-size: .6em;
	font-size: 2.5vw;
}
.dropdesc.hl.ok>div>div {
	top: calc(50% - 1.7vw);
}
.dropzone {
	z-index: 80386;
	height: 50%;
}
#up_dz {
	bottom: 50%;
}
#srch_dz {
	top: 50%;
}
#up_zd {
	top: 12%;
}
#srch_zd {
	bottom: 12%;
}
.dropdesc span {
	color: #fff;
	background: #490;
	border-bottom: .3em solid #6d2;
	text-shadow: 1px 1px 1px #000;
	border-radius: .3em;
	padding: .4em .8em;
	font-size: .4em;
}
.dropdesc.err span {
	background: #904;
	border-color: #d26;
}
#u2form {
	position: absolute;
	top: 0;
	left: 0;
	width: 2px;
	height: 2px;
	overflow: hidden;
}
#u2form input {
	background: var(--bg-u5);
	border: 0px solid var(--bg-u5);
}
#u2err.err {
	color: var(--a-dark);
	padding: .5em;
}
#u2err.msg {
	color: var(--fg-weak);
	padding: .5em;
	font-size: .9em;
}
#u2btn {
	line-height: 1.3em;
	border: .15em dashed var(--u2-btn-b1);
	border-radius: .4em;
	text-align: center;
	font-size: 1.5em;
	margin: .5em auto;
	padding: .8em 0;
	width: 16em;
	cursor: pointer;
}
#op_up2k.srch #u2btn {
	border-color: var(--u2-sbtn-b1);
}
#u2conf.ww #u2btn {
	line-height: 1em;
	padding: .5em 0;
	margin: -2em 1em -3em 0;
}
#u2conf #u2btn {
	padding: .4em 0;
	margin: -2em 0;
	font-size: 1.25em;
	width: 100%;
	max-width: 12em;
	display: inline-block;
}
#u2conf #u2btn_cw {
	text-align: right;
}
#u2bm {
	display: block;
}
#u2bm sup {
	font-weight: bold;
}
#u2notbtn {
	display: none;
	text-align: center;
	background: var(--bg);
	padding-top: 1em;
}
#u2notbtn * {
	line-height: 1.3em;
}
#u2mu div {
	height: 1.2em;
	overflow: hidden;
}
#u2tabw {
	min-height: 0;
	transition: min-height .2s;
	margin: 2em 0;
}
#u2tabw.na>table {
	display: none;
}
#u2tab {
	table-layout: fixed;
	border-collapse: collapse;
	width: calc(100% - 2em);
	max-width: 100em;
	margin: 0 auto;
}
#op_up2k.srch #u2tabf {
	max-width: none;
}
#u2tab td {
	word-wrap: break-word;
	border: 1px solid rgba(128,128,128,0.8);
	border-width: 0 0px 1px 0;
	padding: .2em .3em;
}
#u2tab td:nth-child(2) {
	width: 5em;
	white-space: nowrap;
}
#u2tab td:nth-child(3) {
	width: 40%;
}
#u2tab.up.ok td:nth-child(3),
#u2tab.up.bz td:nth-child(3),
#u2tab.up.q  td:nth-child(3) {
	width: 18em;
}
@media (max-width: 65em) {
	#u2tab {
		font-size: .9em;
	}
}
@media (max-width: 50em) {
	#u2tab.up.ok td:nth-child(3),
	#u2tab.up.bz td:nth-child(3),
	#u2tab.up.q  td:nth-child(3) {
		width: 16em;
	}
}
#op_up2k.srch td.prog {
	font-family: sans-serif;
	font-family: var(--font-main), sans-serif;
	font-size: 1em;
	width: auto;
}
#u2tab tbody tr:hover td {
	background: var(--bg-u2);
}
#u2etas {
	padding: .2em .5em;
	width: 17em;
	cursor: pointer;
	text-align: center;
	white-space: nowrap;
	display: inline-block;
	font-family: 'scp', monospace, monospace;
	font-family: var(--font-mono), 'scp', monospace, monospace;
}
#u2etas.o {
	width: 20em;
}
#u2etas .o {
	display: none;
}
#u2etas.o .o {
	display: inherit;
	display: unset;
}
#u2etaw {
	width: 18em;
	font-size: .94em;
	margin: 1.8em auto .5em auto;
}
#u2etas.o #u2etaw {
	width: 21em;
}
#u2cards {
	padding: 1em 1em .42em 1em;
	margin: 0 auto;
	white-space: nowrap;
	text-align: center;
	overflow: hidden;
	min-width: 24em;
}
#u2cards.w {
	width: 48em;
	text-align: left;
}
#u2cards.ww {
	display: inline-block;
}
#u2etaw.w {
	width: 55em;
	text-align: right;
	margin: 2em auto -2.7em auto;
}
#u2etaw.ww {
	margin: -1em 2em 1em 1em;
}
#u2cards a {
	padding: .2em 1em;
	background: var(--u2-tab-bg);
	border: 1px solid #999;
	border-color: var(--u2-tab-b1);
	border-width: 0 0 1px 0;
}
#u2cards a:first-child {
	border-radius: .4em 0 0 0;
}
#u2cards a:last-child {
	border-radius: 0 .4em 0 0;
}
#u2cards a.act {
	padding-bottom: .5em;
	border-width: 1px 1px .1em 1px;
	border-radius: .3em .3em 0 0;
	margin-left: -1px;
	background: var(--u2-tab-1-bg);
	box-shadow: 0 -.17em .67em var(--u2-tab-1-sh);
	border-color: var(--u2-tab-1-b1) var(--u2-tab-1-b2) var(--bg) var(--u2-tab-1-b2);
	color: var(--u2-tab-1-fg);
	position: relative;
}
#u2cards span {
	color: var(--fg-max);
	font-family: 'scp', monospace;
	font-family: var(--font-mono), 'scp', monospace;
}
#u2cards > a:nth-child(4) > span {
	display: inline-block;
	text-align: center;
	min-width: 1.3em;
}
#u2conf {
	margin: 1em auto;
	width: 30em;
}
#u2conf.w {
	width: 51em;
}
#u2conf.ww {
	width: 82em;
}
#u2conf.ww #u2c3w {
	width: 29em;
}
#u2conf.ww #u2c3w.s {
	width: 39em;
}
#u2conf .c,
#u2conf .c * {
	text-align: center;
	line-height: 1em;
	margin: 0;
	padding: 0;
	border: none;
}
#u2conf .txtbox {
	width: 3em;
	color: var(--fg-max);
	background: var(--u2-txt-bg);
	border: 1px solid #777;
	font-size: 1.2em;
	padding: .15em 0;
	height: 1.05em;
}
#u2conf .txtbox.err {
	color: var(--err-fg);
	background: var(--err-bg);
}
#u2conf a.b {
	color: var(--u2-b-fg);
	background: var(--u2-b1-bg);
	text-decoration: none;
	border-radius: .1em;
	font-size: 1.5em;
	padding: .1em 0;
	margin: 0 -1px;
	width: 1.5em;
	height: 1em;
	display: inline-block;
	position: relative;
	bottom: -0.08em;
}
#u2conf input+a.b {
	background: var(--u2-b2-bg);
}
html.b #u2conf a.b:hover {
	background: var(--btn-h-bg);
}
#u2conf .c label {
	font-size: 1.6em;
	width: 2em;
	height: 1em;
	padding: .4em 0;
	display: block;
	border-radius: .25em;
}
#u2conf input[type="checkbox"] {
	position: relative;
	opacity: .02;
	top: 2em;
}
#u2conf input[type="checkbox"]+label,
#u2conf input[type="checkbox"]:checked+label {
	position: relative;
	cursor: pointer;
	background: var(--btn-bg);
	box-shadow: var(--btn-bs);
	border-bottom: var(--btn-bb);
	text-shadow: 1px 1px 1px #000, 1px -1px 1px #000, -1px -1px 1px #000, -1px 1px 1px #000;
}
#u2conf input[type="checkbox"]:checked+label {
	background: var(--btn-1-bg);
	box-shadow: var(--btn-1-bs);
	border-bottom: var(--btn-1-bb);
}
#u2conf input[type="checkbox"]+label:hover {
	background: var(--btn-h-bg);
	box-shadow: var(--btn-h-bs);
	border-bottom: var(--btn-h-bb);
}
#u2conf input[type="checkbox"]:checked+label:hover {
	background: var(--btn-1h-bg);
	box-shadow: var(--btn-1h-bs);
	border-bottom: var(--btn-1h-bb);
}
#op_up2k.srch #u2conf td:nth-child(2)>*,
#op_up2k.srch #u2conf td:nth-child(3)>* {
	background: #777;
	border-color: var(--fg);
	box-shadow: none;
	opacity: .2;
}
#u2flagblock {
	display: none;
	margin: 2em auto 4em auto;
	padding: .5em;
	max-width: 27em;
	text-align: center;
	border: .2em dashed rgba(128, 128, 128, 0.3);
}
#u2foot,
#u2life {
	color: var(--fg-max);
	text-align: center;
	margin: .8em 0;
}
#u2life {
	margin: 2.5em 0;
	line-height: 1.5em;
}
#u2life div {
	display: inline-block;
	white-space: nowrap;
	margin: 0 2em;
}
#u2life div:first-child {
	margin-bottom: .2em;
}
#u2life small {
	opacity: .6;
}
#lifew {
	border-bottom: 1px dotted var(--fg-max);
}
#u2foot {
	font-size: 1.2em;
	font-style: italic;
}
#u2foot .warn {
	font-size: 1.2em;
	padding: .5em .8em;
	margin: 1em -.6em;
	border-width: .1em 0;
	text-align: center;
}
#u2foot .warn span {
	color: var(--srv-3);
}
#u2foot span {
	color: #999;
	font-size: .9em;
	font-weight: normal;
}
#u2foot>*+* {
	margin-top: 1.5em;
}
#u2life input {
	width: 4em;
	text-align: right;
}
.prog {
	font-family: 'scp', monospace, monospace;
	font-family: var(--font-mono), 'scp', monospace, monospace;
}
#u2tab span.inf,
#u2tab span.ok,
#u2tab span.err {
	color: #fff;
	padding: .22em;
	border-radius: .2em;
	border: 2px solid #f0f;
}
#u2tab span.inf {
	background: var(--u2-inf-bg);
	border-color: var(--u2-inf-b1);
}
#u2tab span.ok {
	background: var(--u2-ok-bg);
	border-color: var(--u2-ok-b1);
}
#u2tab span.err {
	background: var(--u2-err-bg);
	border-color: var(--u2-err-b1);
}
#u2tab a>span,
#docul .bn a>span,
#unpost a>span {
	font-weight: bold;
	font-style: italic;
	color: var(--fg-max);
	padding-left: .2em;
}
.fsearch_explain {
	color: var(--a-dark);
	padding-left: .7em;
	font-size: 1.1em;
	line-height: 0;
}












html.c #path,
html.a #path {
	border-radius: 0 .3em .3em 0;
}
html.c #pctl a,
html.a #pctl a {
	background: rgba(0,0,0,0.1);
	margin-right: .5em;
	box-shadow: -.02em -.02em .3em rgba(0,0,0,0.2) inset;
}
html.d #pctl,
html.b #pctl {
	left: .5em;
}
html.d #ops,
html.c #ops,
html.a #ops {
	margin: 1.7em 1.5em 0 1.5em;
	border-radius: .3em;
	border-width: 1px 0;
}
html.c .opbox,
html.a .opbox {
	margin: 1.5em 0 0 0;
}
html.dz .opview input.i {
	width: calc(100% - 18em);
}
html.c #tree,
html.c #treeh,
html.a #tree,
html.a #treeh {
	border-radius: 0 .3em 0 0;
	background: var(--bg-u2);
}
html.c #treepar,
html.a #treepar {
	background: var(--bg-u2);
}
html.c #tree li,
html.a #tree li {
	border-top: 1px solid var(--bg-u5);
	border-bottom: 1px solid var(--bg-d3);
}
html.c #tree li:last-child,
html.a #tree li:last-child {
	border-bottom: none;
}
html.c .opbox h3,
html.a .opbox h3 {
	border-bottom: 1px solid var(--bg-u5);
}
html.c #ops,
html.c .opbox,
html.c #path,
html.c #srch_form,
html.c .ghead,

html.a #ops,
html.a .opbox,
html.a #path,
html.a #srch_form,
html.a .ghead {
	background: var(--bg-u2);
	border: 1px solid var(--bg-u3);
	box-shadow: 0 0 .3em var(--bg-d3);
}
html.c #u2btn,
html.a #u2btn {
	color: #eee;
	background: var(--bg-u5);
	background: -moz-linear-gradient(top, #367 0%, #489 50%, #38788a 51%, #367 100%);
	background: -webkit-linear-gradient(top, #367 0%, #489 50%, #38788a 51%, #367 100%);
	background: linear-gradient(to bottom, #367 0%, #489 50%, #38788a 51%, #367 100%);
	box-shadow: .4em .4em 0 var(--bg-d3);
	border: 1px solid #222;
}
html.ay #u2btn {
	box-shadow: .4em .4em 0 #ccc;
}
html.dz #u2btn {
	letter-spacing: -.033em;
}
html.c #u2conf.ww #u2btn,
html.a #u2conf.ww #u2btn {
	margin: -2em .5em -3em 0;
	padding: .9em 0;
}
html.c #op_up2k.srch #u2btn,
html.a #op_up2k.srch #u2btn {
	background: linear-gradient(to bottom, #ca3 0%, #fd8 50%, #fc6 51%, #b92 100%);
	text-shadow: 1px 1px 1px #fc6;
	color: #333;
}
html.c #u2conf #u2btn,
html.a #u2conf #u2btn {
	padding: .6em 0;
	margin-top: -2.6em;
}
html.c #u2etas,
html.a #u2etas {
	background: var(--bg-d1);
	border: 1px solid var(--bg-u1);
	border-width: .1em 0;
	border-radius: .5em;
	border-width: .25em 0;
}
html.c #u2cards,
html.a #u2cards {
	margin: 0 auto -1em auto;
}
html.c #u2foot:empty,
html.a #u2foot:empty {
	margin-bottom: -1em;
}
html.ay #ops,
html.ay .opbox,
html.ay #path,
html.ay #doc,
html.ay #srch_form,
html.ay .ghead,
html.ay #u2etas {
	border-color: var(--bg-u2);
	box-shadow: 0 0 .3em var(--bg-u5);
}
html.ay #tree li,
html.ay #treepar {
	border-color: #f7f7f7 var(--bg-max) #ddd var(--bg-max);
}
html.ay #path {
	background: #f7f7f7;
	box-shadow: 0 0 .3em #bbb;
}
html.ay #treeh,
html.ay #treepar {
	background: #f7f7f7;
	border-color: #ddd;
}
html.ay #tree {
	border-color: #ddd;
	box-shadow: 0 0 1em #ddd;
	background: #f7f7f7;
}





html.b #path {
	margin: .3em 0;
	line-height: 1.7em;
}
html.b.op_open #path {
	margin-top: .2em;
}
html.b #srv_info {
	display: none;
}
html.b #srv_info2 {
	display: inline-block;
}
html.b #srv_info2:after {
	content: '//';
	margin: 0 .4em;
}
html.b #acc_info {
	right: .5em;
}
html.b #wtoggle {
	border-radius: .1em 0 0 0;
}
html.d #barpos,
html.d #barbuf,
html.d #pvol,
html.b #barpos,
html.b #barbuf,
html.b #pvol {
	border-radius: .2em;
}
html.b #barpos {
	box-shadow: 0 0 0 1px rgba(0,0,0,0.4);
}
html.by #barpos {
	box-shadow: 0 0 0 1px rgba(0,0,0,0.2) inset;
}
html.b #ops {
	max-width: 1em;
	margin-bottom: 1.7em;
	position: relative;
	z-index: 2;
}
html.b #ops a {
	background: var(--bg);
}
html.b .opview {
	margin: 1em 0;
}
html.b #srch_q {
	margin: .2em 0 0 1.6em;
}
html.b #srch_q:empty {
	margin-bottom: -1em;
}
html.b #op_up2k {
	margin-top: 3em;
}
html.b #tree {
	box-shadow: 0 -1px 0 rgba(128,128,128,0.4);
}
html.bz #tree {
	box-shadow: 0 -1px 0 var(--bg-d3);
}
html.b #treeh,
html.b #tree li {
	border: none;
}
html.b #tree li {
	margin-left: .8em;
}
html.b #docul a,
html.b .ntree a {
	padding: .6em .2em;
}
html.b #treepar {
	margin-left: .63em;
	width: calc(.1em + var(--nav-sz) - var(--sbw));
	border-bottom: .2em solid var(--f-h-b1);
}
html.b #wrap {
	margin-top: 2em;
}
html.by .ghead,
html.bz .ghead {
	background: var(--bg);
	padding: .2em 0;
}
html.b #files td {
	padding: .5em .7em;
}
html.b #ggrid>a {
	margin: .8em;
}
html.b .btn {
	top: -.1em;
}
html.b #op_up2k.srch sup {
	color: #fc0;
}
html.by #u2btn sup {
	color: #06b;
}
html.by #op_up2k.srch sup {
	color: #b70;
}
html.bz #u2cards a.act {
	box-shadow: 0 -.1em .2em var(--bg-d2);
}
html.b #u2conf {
	margin: 2em auto 0 auto;
}
html.b #u2conf .txtbox {
	border: none;
}
html.b #u2conf a.b {
	border-radius: .2em;
}
html.by #u2cards a.act {
	border-width: 2px;
}





html.cy .mdo a {
	background: #f00;
}
html.cy #wrap,
html.cy #acc_info a,
html.cy #op_up2k,
html.cy #files,
html.cy #files a,
html.cy #files tbody div a:last-child {
	color: #000;
}
html.cy #u2tab a,
html.cy #u2cards a {
	color: #f00;
}
html.cy #unpost a {
	color: #ff0;
}
html.cy #barbuf {
	filter: hue-rotate(267deg) brightness(0.8) contrast(4);
}
html.cy #pvol {
	filter: hue-rotate(4deg) contrast(2.2);
}





html.dz * {
	border-radius: 0 !important;
}
html.d #treepar {
	border-bottom: .2em solid var(--f-h-b1);
}




@media (max-width: 32em) {
	#u2conf {
		font-size: .9em;
	}
}
@media (max-width: 28em) {
	#u2conf {
		font-size: .8em;
	}
}
@media (min-width: 70em) {
	#barpos,
	#barbuf {
		width: calc(100% - 21em);
		left: 9.8em;
		top: .7em;
		height: 1.6em;
		bottom: auto;
	}
	html.d #barpos,
	html.b #barpos,
	html.d #barbuf,
	html.b #barbuf {
		width: calc(100% - 19em);
		left: 8em;
	}
	#widget {
		bottom: -3.2em;
		height: 3.2em;
	}
	#pvol {
		max-width: 9em;
	}
	html.d #ops,
	html.b #ops {
		padding-left: 1.7em;
	}
	html.d .opview,
	html.b .opview {
		margin: 1em;
	}
	html.d #path,
	html.b #path {
		padding-left: 1.3em;
	}
}
@media (max-width: 35em) {
	#ops>a[data-dest="new_md"],
	#ops>a[data-dest="msg"] {
		display: none;
	}
	#op_mkdir.act+div,
	#op_mkdir.act+div+div {
		display: block;
		margin-top: 1em;
	}
}
@media (max-width: 54em) {
	html.b #ops {
		margin-top: 1.7em;
	}
}
@supports (display: grid) and (gap: 1em) {
	#ggrid {
		display: grid;
		margin: 0em 0.25em;
		padding: unset;
		grid-template-columns: repeat(auto-fit,var(--grid-sz));
		justify-content: center;
		gap: 1em;
	}

	html.b #ggrid {
		padding: 0 2em 2em 0;
		gap: .5em 3em;
	}

	#ggrid > a {
		margin: unset;
		padding: unset;
  	}

  	#ggrid>a>span {
		text-align: center;
		padding: .2em .2em .15em .2em;
	}
}





@media (prefers-reduced-motion) {
	@keyframes spin { }
	@keyframes gexit { }
	@keyframes bounce { }
	@keyframes bounceFromLeft { }
	@keyframes bounceFromRight { }

	#ggrid>a:before,
	#widget.anim,
	#u2tabw,
	.dropdesc,
	.dropdesc b,
	.dropdesc>div>div {
		transition: none;
	}
	#bbox-next,
	#bbox-prev,
	#bbox-btns {
		transition: background-color .3s ease, color .3s ease;
	}
}
