<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<title>{{ s_doctitle }}</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=0.8">
    <meta name="robots" content="noindex, nofollow">
	<meta name="theme-color" content="#{{ tcolor }}">
	<link rel="stylesheet" media="screen" href="{{ r }}/.cpr/shares.css?_={{ ts }}">
	<link rel="stylesheet" media="screen" href="{{ r }}/.cpr/ui.css?_={{ ts }}">
{{ html_head }}
</head>

<body>
	<div id="wrap">
		<a href="{{ r }}/?idp">refresh</a>
		<a href="{{ r }}/?h">control-panel</a>

        <table id="tab"><thead><tr>
            <th>forget</th>
            <th>user</th>
            <th>groups</th>
        </tr></thead><tbody>
        {% for un, gn in rows %}
        <tr>
            <td><a href="{{ r }}/?idp=rm={{ un|e }}">forget</a></td>
            <td>{{ un|e }}</td>
            <td>{{ gn|e }}</td>
        </tr>
        {% endfor %}
        </tbody></table>
        {% if not rows %}
        (there are no IdP users in the cache)
        {% endif %}
    </div>
	<a href="#" id="repl">π</a>
	<script>

var SR="{{ r }}",
	lang="{{ lang }}",
	dfavico="{{ favico }}";

var STG = window.localStorage;
document.documentElement.className = (STG && STG.cpp_thm) || "{{ this.args.theme }}";

</script>
<script src="{{ r }}/.cpr/util.js?_={{ ts }}"></script>
{%- if js %}
<script src="{{ js }}_={{ ts }}"></script>
{%- endif %}
</body>
</html>

