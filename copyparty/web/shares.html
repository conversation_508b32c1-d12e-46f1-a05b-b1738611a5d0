<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<title>{{ s_doctitle }}</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=0.8">
    <meta name="robots" content="noindex, nofollow">
	<meta name="theme-color" content="#{{ tcolor }}">
	<link rel="stylesheet" media="screen" href="{{ r }}/.cpr/shares.css?_={{ ts }}">
	<link rel="stylesheet" media="screen" href="{{ r }}/.cpr/ui.css?_={{ ts }}">
{{ html_head }}
</head>

<body>
	<div id="wrap">
		<a href="{{ r }}/?shares">refresh</a>
		<a href="{{ r }}/?h">control-panel</a>

        <span>axs = perms (read,write,move,delet)</span>
        <span>nf = numFiles (0=dir)</span>
        <span>min/hrs = time left</span>

        <table id="tab"><thead><tr>
            <th>sharekey</th>
            <th>delete</th>
            <th>pw</th>
            <th>source</th>
            <th>axs</th>
            <th>nf</th>
            <th>user</th>
            <th>created</th>
            <th>expires</th>
            <th>min</th>
            <th>hrs</th>
            <th>add time</th>
        </tr></thead><tbody>
        {% for k, pw, vp, pr, st, un, t0, t1 in rows %}
        <tr>
            <td>
                <a href="{{ r }}{{ shr }}{{ k }}?qr">qr</a>
                <a href="{{ r }}{{ shr }}{{ k }}">{{ k }}</a>
            </td>
            <td><a href="#" k="{{ k }}">delete</a></td>
            <td>{{ "yes" if pw else "--" }}</td>
            <td><a href="{{ r }}/{{ vp|e }}">/{{ vp|e }}</a></td>
            <td>{{ pr }}</td>
            <td>{{ st }}</td>
            <td>{{ un|e }}</td>
            <td>{{ t0 }}</td>
            <td>{{ t1 }}</td>
            <td>{{ "inf" if not t1 else "dead" if t1 < now else ((t1 - now) / 60)   | round(1) }}</td>
            <td>{{ "inf" if not t1 else "dead" if t1 < now else ((t1 - now) / 3600) | round(1) }}</td>
            <td></td>
        </tr>
        {% endfor %}
        </tbody></table>
        {% if not rows %}
        (you don't have any active shares btw)
        {% endif %}
    </div>
	<a href="#" id="repl">π</a>
	<script>

var SR="{{ r }}",
    shr="{{ shr }}",
	lang="{{ lang }}",
    dutc={{ this.args.js_utc }},
	dfavico="{{ favico }}";

var STG = window.localStorage;
document.documentElement.className = (STG && STG.cpp_thm) || "{{ this.args.theme }}";

</script>
<script src="{{ r }}/.cpr/util.js?_={{ ts }}"></script>
<script src="{{ r }}/.cpr/shares.js?_={{ ts }}"></script>
{%- if js %}
<script src="{{ js }}_={{ ts }}"></script>
{%- endif %}
</body>
</html>

