<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<title>{{ s_doctitle }}</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=0.8">
	<meta name="theme-color" content="#{{ tcolor }}">
	<link rel="stylesheet" media="screen" href="{{ r }}/.cpr/splash.css?_={{ ts }}">
	<link rel="stylesheet" media="screen" href="{{ r }}/.cpr/ui.css?_={{ ts }}">
{{ html_head }}
</head>

<body>
	<div id="wrap">
		{%- if not in_shr %}
		<a id="a" href="{{ r }}/?h" class="af">refresh</a>
		<a id="v" href="{{ r }}/?hc" class="af">connect</a>

		{%- if this.uname == '*' %}
			<p id="b">howdy stranger &nbsp; <small>(you're not logged in)</small></p>
		{%- else %}
			<a id="c" href="{{ r }}/?pw=x" class="logout">logout</a>
			<p><span id="m">welcome back,</span> <strong>{{ this.uname|e }}</strong></p>
		{%- endif %}
		{%- endif %}

		{%- if msg %}
		<div id="msg">
			{{ msg }}
		</div>
		{%- endif %}

		{%- if ups %}
		<h1 id="aa">incoming files:</h1>
			<table class="vols">
				<thead><tr><th>%</th><th>speed</th><th>eta</th><th>idle</th><th>dir</th><th>file</th></tr></thead>
				<tbody>
					{% for u in ups %}
					<tr><td>{{ u[0] }}</td><td>{{ u[1] }}</td><td>{{ u[2] }}</td><td>{{ u[3] }}</td><td><a href="{{ u[4] }}">{{ u[5]|e }}</a></td><td>{{ u[6]|e }}</td></tr>
					{% endfor %}
				</tbody>
			</table>
		{%- endif %}

		{%- if dls %}
		<h1 id="ae">active downloads:</h1>
			<table class="vols">
				<thead><tr><th>%</th><th>sent</th><th>speed</th><th>eta</th><th>idle</th><th></th><th>dir</th><th>file</th></tr></thead>
				<tbody>
					{% for u in dls %}
					<tr><td>{{ u[0] }}</td><td>{{ u[1] }}</td><td>{{ u[2] }}</td><td>{{ u[3] }}</td><td>{{ u[4] }}</td><td>{{ u[5] }}</td><td><a href="{{ u[6] }}">{{ u[7]|e }}</a></td><td>{{ u[8] }}</td></tr>
					{% endfor %}
				</tbody>
			</table>
		{%- endif %}

		{%- if avol %}
		<h1>admin panel:</h1>
		<table><tr><td> <!-- hehehe -->
			<table class="num">
				<tr><td>scanning</td><td>{{ scanning }}</td></tr>
				<tr><td>hash-q</td><td>{{ hashq }}</td></tr>
				<tr><td>tag-q</td><td>{{ tagq }}</td></tr>
				<tr><td>mtp-q</td><td>{{ mtpq }}</td></tr>
				<tr><td>db-act</td><td id="u">{{ dbwt }}</td></tr>
			</table>
		</td><td>
			<table class="vols">
				<thead><tr><th>vol</th><th id="t">action</th><th>status</th></tr></thead>
				<tbody>
					{% for mp in avol %}
					{%- if mp in vstate and vstate[mp] %}
					<tr><td><a href="{{ r }}{{ mp }}{{ url_suf }}">{{ mp }}</a></td><td><a class="s" href="{{ r }}{{ mp }}?scan">rescan</a></td><td>{{ vstate[mp] }}</td></tr>
					{%- endif %}
					{% endfor %}
				</tbody>
			</table>
		</td></tr></table>
		<div class="btns">
			<a id="d" href="{{ r }}/?stack">dump stack</a>
			<a id="e" href="{{ r }}/?reload=cfg">reload cfg</a>
		</div>
		{%- endif %}

		{%- if rvol %}
		<h1 id="f">you can browse:</h1>
		<ul>
			{% for mp in rvol %}
			<li><a href="{{ r }}{{ mp }}{{ url_suf }}">{{ mp }}</a></li>
			{% endfor %}
		</ul>
		{%- endif %}

		{%- if wvol %}
		<h1 id="g">you can upload to:</h1>
		<ul>
			{% for mp in wvol %}
			<li><a href="{{ r }}{{ mp }}{{ url_suf }}">{{ mp }}</a></li>
			{% endfor %}
		</ul>
		{%- endif %}

		{%- if in_shr %}
		<h1 id="z">unlock this share:</h1>
		<div>
			<form id="lf" method="post" enctype="multipart/form-data" action="{{ r }}/{{ qvpath }}">
				<input type="hidden" id="la" name="act" value="login" />
				<input type="password" id="lp" name="cppwd" placeholder=" password" />
				<input type="hidden" name="uhash" id="uhash" value="x" />
				<input type="submit" id="ls" value="Unlock" />
				{% if ahttps %}
				<a id="w" href="{{ ahttps }}">switch to https</a>
				{% endif %}
			</form>
		</div>
		{%- else %}
		<h1 id="l">login for more:</h1>
		<div>
			<form id="lf" method="post" enctype="multipart/form-data" action="{{ r }}/{{ qvpath }}">
				<input type="hidden" id="la" name="act" value="login" />
				<input type="password" id="lp" name="cppwd" placeholder=" password" />
				<input type="hidden" name="uhash" id="uhash" value="x" />
				<input type="submit" id="ls" value="login" />
				{% if chpw %}
				<a id="x" href="#">change password</a>
				{% endif %}
				{% if ahttps %}
				<a id="w" href="{{ ahttps }}">switch to https</a>
				{% endif %}
			</form>
		</div>
		{%- endif %}

		<h1 id="cc">other stuff:</h1>
		<ul>
			{%- if this.uname in this.args.idp_adm_set %}
			<li><a id="ag" href="{{ r }}/?idp">view idp cache</a></li>
			{% endif %}

			{%- if this.uname != '*' and this.args.shr %}
			<li><a id="y" href="{{ r }}/?shares">edit shares</a></li>
			{% endif %}

			{% if k304 or k304vis %}
			{% if k304 %}
			<li><a id="h" href="{{ r }}/?cc&setck=k304=n">disable k304</a> (currently enabled)
			{%- else %}
			<li><a id="i" href="{{ r }}/?cc&setck=k304=y" class="r">enable k304</a> (currently disabled)
			{% endif %}
			<blockquote id="j">enabling k304 will disconnect your client on every HTTP 304, which can prevent some buggy proxies from getting stuck (suddenly not loading pages), <em>but</em> it will also make things slower in general</blockquote></li>
			{% endif %}

			{% if no304 or no304vis %}
			{% if no304 %}
			<li><a id="ab" href="{{ r }}/?cc&setck=no304=n">disable no304</a> (currently enabled)
			{%- else %}
			<li><a id="ac" href="{{ r }}/?cc&setck=no304=y" class="r">enable no304</a> (currently disabled)
			{% endif %}
			<blockquote id="ad">enabling no304 will disable all caching; try this if k304 wasn't enough. This will waste a huge amount of network traffic!</blockquote></li>
			{% endif %}

			<li><a id="af" href="{{ r }}/?ru">show recent uploads</a></li>
			<li><a id="k" href="{{ r }}/?reset" class="r" onclick="localStorage.clear();return true">reset client settings</a></li>
		</ul>

	</div>
	<a href="#" id="repl">π</a>
	{%- if not this.args.nb %}
	<span id="pb"><span>powered by</span> <a href="{{ this.args.pb_url }}">copyparty {{ver}}</a></span>
	{%- endif %}
	<script>

var SR="{{ r }}",
	lang="{{ lang }}",
	dfavico="{{ favico }}";

var STG = window.localStorage;
document.documentElement.className = (STG && STG.cpp_thm) || "{{ this.args.theme }}";

</script>
<script src="{{ r }}/.cpr/util.js?_={{ ts }}"></script>
<script src="{{ r }}/.cpr/splash.js?_={{ ts }}"></script>
{%- if js %}
<script src="{{ js }}_={{ ts }}"></script>
{%- endif %}
</body>
</html>

