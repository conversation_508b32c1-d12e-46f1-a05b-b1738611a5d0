<!DOCTYPE html><html><head>
	<meta charset="utf-8">
	<title>📝 {{ title }}</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=0.7">
	<meta name="theme-color" content="#{{ tcolor }}">
	<link rel="stylesheet" href="{{ r }}/.cpr/ui.css?_={{ ts }}">
	<link rel="stylesheet" href="{{ r }}/.cpr/mde.css?_={{ ts }}">
	<link rel="stylesheet" href="{{ r }}/.cpr/deps/mini-fa.css?_={{ ts }}">
	<link rel="stylesheet" href="{{ r }}/.cpr/deps/easymde.css?_={{ ts }}">
{{ html_head }}
</head>
<body>
	<div id="mw">
		<div id="mn"></div>
		<div id="ml">
			<div style="text-align:center;margin:5em 0">
				<div style="font-size:2em;margin:1em 0">Loading</div>
				if you're still reading this, check that javascript is allowed
			</div>
		</div>
		<div id="m">
			<textarea id="mt" style="display:none" autocomplete="off">{{ md }}</textarea>
		</div>
	</div>
	<a href="#" id="repl">π</a>
	<script>

var SR = "{{ r }}",
	last_modified = {{ lastmod }},
	have_emp = {{ "true" if have_emp else "false" }},
	dfavico = "{{ favico }}";

var md_opt = {
	link_md_as_html: false,
	modpoll_freq: {{ md_chk_rate }}
};

var lightswitch = (function () {
	var l = window.localStorage,
		drk = (l && l.light) != 1,
		f = function (e) {
if (e) drk = !drk;
document.documentElement.className = drk? "z":"y";
try { l.light = drk? 0:1; } catch (ex) { }
		};
	f();
	return f;
})();

	</script>
	<script src="{{ r }}/.cpr/util.js?_={{ ts }}"></script>
	<script src="{{ r }}/.cpr/deps/marked.js?_={{ ts }}"></script>
	<script src="{{ r }}/.cpr/deps/easymde.js?_={{ ts }}"></script>
	<script src="{{ r }}/.cpr/mde.js?_={{ ts }}"></script>
	{%- if js %}
	<script src="{{ js }}_={{ ts }}"></script>
	{%- endif %}
</body></html>

