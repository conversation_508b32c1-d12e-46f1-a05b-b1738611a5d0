# python
__pycache__/
*.py[cod]
*$py.class
MANIFEST.in
MANIFEST
copyparty.egg-info/
.venv/

/buildenv/
/build/
/dist/
/py2/
/sfx*
/pyz/
/unt/
/log/

# ide
*.sublime-workspace

# winmerge
*.bak

# apple pls
.DS_Store

# derived
copyparty/res/COPYING.txt
copyparty/web/deps/
srv/
scripts/docker/i/
scripts/deps-docker/uncomment.py
contrib/package/arch/pkg/
contrib/package/arch/src/

# state/logs
up.*.txt
.hist/
scripts/docker/*.out
scripts/docker/*.err
/perf.*

# nix build output link
result
