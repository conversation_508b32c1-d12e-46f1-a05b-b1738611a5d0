#!/bin/bash
set -e

( xsel -ob | sed -r '
s`/home/<USER>/`~/`;
s/uuid:[0-9a-f-]{36}/autogenerated/;
s/(-salt SALT.*default: )[0-9a-zA-Z/+]{24}\)/\124-character-autogenerated)/;
s/(-salt SALT.*default: )[0-9a-zA-Z/+]{40}\)/\140-character-autogenerated)/;
s/(--name TXT.*default: )[^)]+/\1hostname/;
s/(--hash-mt CORES.*default: )[0-9]+/\1numCores if 5 or less/;
s/(--mtag-mt|th-mt)( CORES.*default: )[0-9]+/\1\2numCores/;
s/(--th-ram-max GB.*default: )[0-9\.]+/\1dynamic/;
' | awk '
/^copyparty/{a=1} !a{next}
/^0{20}/{b=1} b&&/^copyparty v[0-9]+\./{s=3}
s{s-=1;next} 1' |
head -n-6; echo eof ) >helptext.txt
exit 0


# =====================================================================
# end of script;  below is the explanation how to use this:


# first open an infinitely wide console (this is why you own an ultrawide) and copypaste this into it:
for a in '' -bind -accounts -flags -handlers -hooks -urlform -exp -ls -dbd -chmod -pwhash -zm; do
./copyparty-sfx.py --help$a 2>/dev/null; printf '\n\n\n%0255d\n\n\n'; done

# then copypaste all of the output by pressing ctrl-shift-a, ctrl-shift-c
# and finally actually run this script which should produce helptext.txt
