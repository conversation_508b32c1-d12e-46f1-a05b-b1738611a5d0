# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,2,3,0),
    prodvers=(1,2,3,0),
    mask=0x3f,
    flags=0x0,
    OS=0x4,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        '000004b0',
        [StringStruct('CompanyName', 'ocv.me'),
        StringStruct('FileDescription', 'copyparty file server'),
        StringStruct('FileVersion', '1.2.3'),
        StringStruct('InternalName', 'copyparty'),
        StringStruct('LegalCopyright', '2019, ed'),
        StringStruct('OriginalFilename', 'copyparty.exe'),
        StringStruct('ProductName', 'copyparty'),
        StringStruct('ProductVersion', '1.2.3')])
      ]), 
    VarFileInfo([VarStruct('Translation', [0, 1200])])
  ]
)
