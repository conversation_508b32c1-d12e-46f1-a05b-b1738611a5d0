after performing all the initial setup in this file,
run ./build.sh in git-bash to build + upload the exe

to obtain the files referenced below, see ./deps.txt

and if you don't yet have a windows vm to build in, then the
first step will be "creating the windows VM templates" below

commands to start the VMs after initial setup:
qemu-system-x86_64 -m 4096 -enable-kvm --machine q35 -cpu host -smp 4 -usb -device usb-tablet -net bridge,br=virhost0 -net nic,model=e1000e -drive file=win7-x32.qcow2,discard=unmap,detect-zeroes=unmap
qemu-system-x86_64 -m 4096 -enable-kvm --machine q35 -cpu host -smp 4 -usb -device usb-tablet -net bridge,br=virhost0 -net nic,model=virtio -drive file=win10-e2021.qcow2,if=virtio,discard=unmap



## ============================================================
## first-time setup in a stock win7x32sp1 and/or win10x64 vm:
## 

grab & install from ftp2host:  Git-2.45.2-32-bit.exe

...and do this on the host so you can grab these notes too:
unix2dos <~/dev/copyparty/scripts/pyinstaller/notes.txt >~/dev/pyi/notes.txt


===[ copy-paste into git-bash ]================================
uname -s | grep NT-10 && w10=1 || {
  w7=1; uname -s | grep WOW64 && w7x64=1 || w7x32=1
}
fns=(
  altgraph-0.17.4-py2.py3-none-any.whl
  pefile-2023.2.7-py3-none-any.whl
  pywin32_ctypes-0.2.3-py3-none-any.whl
  upx-5.0.1-win32.zip
)
[ $w10 ] && fns+=(
  jinja2-3.1.6-py3-none-any.whl
  MarkupSafe-3.0.2-cp313-cp313-win_amd64.whl
  mutagen-1.47.0-py3-none-any.whl
  packaging-25.0-py3-none-any.whl
  pillow-11.3.0-cp313-cp313-win_amd64.whl
  pyinstaller-6.14.1-py3-none-win_amd64.whl
  pyinstaller_hooks_contrib-2025.5-py3-none-any.whl
  python-3.13.5-amd64.exe
  setuptools-80.9.0-py3-none-any.whl
)
[ $w7 ] && fns+=(
  future-1.0.0-py3-none-any.whl
  importlib_metadata-6.7.0-py3-none-any.whl
  packaging-24.0-py3-none-any.whl
  pip-24.0-py3-none-any.whl
  pyinstaller_hooks_contrib-2023.8-py2.py3-none-any.whl
  setuptools-70.3.0-py3-none-any.whl
  typing_extensions-4.7.1-py3-none-any.whl
  zipp-3.15.0-py3-none-any.whl
)
[ $w7x64 ] && fns+=(
  windows6.1-kb2533623-x64.msu
  pyinstaller-5.13.2-py3-none-win64.whl
  python-3.7.9-amd64.exe
)
[ $w7x32 ] && fns+=(
  windows6.1-kb2533623-x86.msu
  pyinstaller-5.13.2-py3-none-win32.whl
  python-3.7.9.exe
)
dl() { curl -fkLOC- "$1" && return 0; echo "$1"; return 1; }
cd ~/Downloads && rm -f Git-*.exe &&
for fn in "${fns[@]}"; do
  dl "https://*************:3923/ro/pyi/$fn" || {
    echo ERROR; ok=; break
  }
done


WIN7-ONLY: manually install windows6.1-kb2533623 and reboot

manually install python-3.99.99.exe and then delete it

close and reopen git-bash so python is in PATH


===[ copy-paste into git-bash ]================================
uname -s | grep NT-10 && w10=1 || w7=1
[ $w7 ] && pyv=37 || pyv=313
appd=$(cygpath.exe "$APPDATA")
cd ~/Downloads &&
yes | unzip upx-*-win32.zip &&
mv upx-*/upx.exe . &&
python -m ensurepip &&
{ [ $w10 ] || python -m pip install --user -U pip-*.whl; } &&
python -m pip install --user -U packaging-*.whl &&
{ [ $w7 ] || python -m pip install --user -U {setuptools,mutagen,pillow,jinja2,MarkupSafe}-*.whl; } &&
{ [ $w10 ] || python -m pip install --user -U future-*.whl importlib_metadata-*.whl typing_extensions-*.whl zipp-*.whl; } &&
python -m pip install --user -U pyinstaller-*.whl pefile-*.whl pywin32_ctypes-*.whl pyinstaller_hooks_contrib-*.whl altgraph-*.whl &&
sed -ri 's/--lzma/--best/' $appd/Python/Python$pyv/site-packages/pyinstaller/building/utils.py &&
curl -fkLO https://*************:3923/cpp/scripts/uncomment.py &&
python uncomment.py 1 $(for d in $appd/Python/Python$pyv/site-packages/{mutagen,PIL,jinja2,markupsafe}; do find $d -name \*.py; done) &&
cd &&
rm -f build.sh &&
curl -fkLO https://*************:3923/cpp/scripts/pyinstaller/build.sh &&
{ [ $w10 ] || curl -fkLO https://*************:3923/cpp/scripts/pyinstaller/up2k.sh; } &&
echo ok


now is an excellent time to take another snapshot, but:
* on win7: first do the 4g.nul thing again
* on win10: first do a reboot so fstrim kicks in
then shutdown and:  vmsnap the.qcow2 snap2





## ============================================================
## creating the windows VM templates
##

bash ~/dev/asm/doc/setup-virhost.sh  # github:9001/asm
truncate -s 4G ~/dev/pyi/4g.nul  # win7 "trim"

note: if you keep accidentally killing the vm with alt-f4 then remove "-device usb-tablet" in the qemu commands below

# win7: don't bother with virtio stuff since win7 doesn't fstrim properly anyways (4g.nul takes care of that)
rm -f win7-x32.qcow2
qemu-img create -f qcow2 win7-x32.qcow2 64g
qemu-system-x86_64 -m 4096 -enable-kvm --machine q35 -cpu host -smp 4 -usb -device usb-tablet -net bridge,br=virhost0 -net nic,model=e1000e -drive file=win7-x32.qcow2,discard=unmap,detect-zeroes=unmap \
  -cdrom ~/iso/win7-X17-59183-english-32bit-professional.iso

# win10: use virtio hdd and net (viostor+netkvm), but do NOT use qxl graphics (kills mouse cursor)
rm -f win10-e2021.qcow2
qemu-img create -f qcow2 win10-e2021.qcow2 64g
qemu-system-x86_64 -m 4096 -enable-kvm --machine q35 -cpu host -smp 4 -usb -device usb-tablet -net bridge,br=virhost0 -net nic,model=virtio -drive file=win10-e2021.qcow2,if=virtio,discard=unmap \
  -drive file=$HOME/iso/virtio-win-0.1.248.iso,media=cdrom -cdrom $HOME/iso/en-us_windows_10_enterprise_ltsc_2021_x64_dvd_d289cf96.iso

tweak stuff to your preference, but also do these steps in order:
* press ctrl-alt-g so you don't accidentally alt-f4 the vm
* startmenu, type "sysdm.cpl" and hit Enter,
  * system protection -> configure -> disable
  * advanced > performance > advanced > virtual memory > no paging file
* startmenu, type "cmd" and hit Ctrl-Shift-Enter, run command:  powercfg /h off
* reboot
* make screen resolution something comfy (1440x900 is always a winner)
* cmd.exe window-width 176 (assuming 1440x900) and buffer-height 8191
* fix explorer settings (show hidden files and file extensions)
* WIN10-ONLY: startmenu, device manager, install netkvm driver for ethernet
* create ftp2host.bat on desktop with following contents:
    start explorer ftp://wark:k@*************:3921/ro/pyi/
* WIN7-ONLY: connect to ftp, download 4g.nul to desktop, then delete it (poor man's fstrim...)

and finally take snapshots of the VMs by copypasting this stuff into your shell:
vmsnap() { zstd --long=31 -vT0 -19 <$1 >$1.$2; };
vmsnap win7-x32.qcow2 snap1
vmsnap win10-e2021.qcow2 snap1

note: vmsnap could have defragged the qcow2 as well, but
  that makes it hard to do xdelta3 memes so it's not worth it --
  but you can add this before "zstd" if you still want to:
  qemu-img convert -f qcow2 -O qcow2 $1 a.qcow2 && mv a.qcow2 $1 &&





## ============================================================
## notes
##

size   t-unpack  virustotal                     cmnt
8059k  0m0.375s  5/70 generic-only, sandbox-ok  no-upx
7095k  0m0.563s  4/70 generic-only, sandbox-ok  standard-upx
6958k  0m0.578s  7/70 generic-only, sandbox-ok  upx+upx

use python 3.7 since 3.8 onwards requires KB2533623 on target

generate loader.rc template:
%appdata%\python\python37\scripts\pyi-grab_version C:\Users\<USER>\AppData\Local\Programs\Python\Python37\python.exe

