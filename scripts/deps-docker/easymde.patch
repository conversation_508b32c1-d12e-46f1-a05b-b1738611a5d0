diff -wNarU2 easy-markdown-editor-2.16.1-orig/gulpfile.js easy-markdown-editor-2.16.1/gulpfile.js
--- easy-markdown-editor-2.16.1-orig/gulpfile.js	2022-01-14 23:27:44.000000000 +0100
+++ easy-markdown-editor-2.16.1/gulpfile.js	2022-02-09 23:06:01.694592535 +0100
@@ -25,5 +25,4 @@
     './node_modules/codemirror/lib/codemirror.css',
     './src/css/*.css',
-    './node_modules/codemirror-spell-checker/src/css/spell-checker.css',
 ];
 
diff -wNarU2 easy-markdown-editor-2.16.1-orig/package.json easy-markdown-editor-2.16.1/package.json
--- easy-markdown-editor-2.16.1-orig/package.json	2022-01-14 23:27:44.000000000 +0100
+++ easy-markdown-editor-2.16.1/package.json	2022-02-09 23:06:24.778501888 +0100
@@ -23,5 +23,4 @@
         "@types/marked": "^4.0.1",
         "codemirror": "^5.63.1",
-        "codemirror-spell-checker": "1.1.2",
         "marked": "^4.0.10"
     },
diff -wNarU2 easy-markdown-editor-2.16.1-orig/src/js/easymde.js easy-markdown-editor-2.16.1/src/js/easymde.js
--- easy-markdown-editor-2.16.1-orig/src/js/easymde.js	2022-01-14 23:27:44.000000000 +0100
+++ easy-markdown-editor-2.16.1/src/js/easymde.js	2022-02-09 23:07:21.203131415 +0100
@@ -12,5 +12,4 @@
 require('codemirror/mode/gfm/gfm.js');
 require('codemirror/mode/xml/xml.js');
-var CodeMirrorSpellChecker = require('codemirror-spell-checker');
 var marked = require('marked').marked;
 
@@ -1816,9 +1815,4 @@
         options.autosave.uniqueId = options.autosave.unique_id;
 
-    // If overlay mode is specified and combine is not provided, default it to true
-    if (options.overlayMode && options.overlayMode.combine === undefined) {
-        options.overlayMode.combine = true;
-    }
-
     // Update this options
     this.options = options;
@@ -2057,34 +2051,7 @@
     var mode, backdrop;
 
-    // CodeMirror overlay mode
-    if (options.overlayMode) {
-        CodeMirror.defineMode('overlay-mode', function (config) {
-            return CodeMirror.overlayMode(CodeMirror.getMode(config, options.spellChecker !== false ? 'spell-checker' : 'gfm'), options.overlayMode.mode, options.overlayMode.combine);
-        });
-
-        mode = 'overlay-mode';
-        backdrop = options.parsingConfig;
-        backdrop.gitHubSpice = false;
-    } else {
         mode = options.parsingConfig;
         mode.name = 'gfm';
         mode.gitHubSpice = false;
-    }
-    if (options.spellChecker !== false) {
-        mode = 'spell-checker';
-        backdrop = options.parsingConfig;
-        backdrop.name = 'gfm';
-        backdrop.gitHubSpice = false;
-
-        if (typeof options.spellChecker === 'function') {
-            options.spellChecker({
-                codeMirrorInstance: CodeMirror,
-            });
-        } else {
-            CodeMirrorSpellChecker({
-                codeMirrorInstance: CodeMirror,
-            });
-        }
-    }
 
     // eslint-disable-next-line no-unused-vars
diff -wNarU2 easy-markdown-editor-2.16.1-orig/types/easymde.d.ts easy-markdown-editor-2.16.1/types/easymde.d.ts
--- easy-markdown-editor-2.16.1-orig/types/easymde.d.ts	2022-01-14 23:27:44.000000000 +0100
+++ easy-markdown-editor-2.16.1/types/easymde.d.ts	2022-02-09 23:07:55.427605243 +0100
@@ -167,9 +167,4 @@
     }
 
-    interface OverlayModeOptions {
-        mode: CodeMirror.Mode<any>;
-        combine?: boolean;
-    }
-
     interface SpellCheckerOptions {
         codeMirrorInstance: CodeMirror.Editor;
@@ -229,6 +224,4 @@
         syncSideBySidePreviewScroll?: boolean;
 
-        overlayMode?: OverlayModeOptions;
-
         direction?: 'ltr' | 'rtl';
     }
