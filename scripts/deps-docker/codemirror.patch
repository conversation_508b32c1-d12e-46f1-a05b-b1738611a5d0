diff -wNarU2 codemirror-5.65.1-orig/mode/gfm/gfm.js codemirror-5.65.1/mode/gfm/gfm.js
--- codemirror-5.65.1-orig/mode/gfm/gfm.js	2022-01-20 13:06:23.000000000 +0100
+++ codemirror-5.65.1/mode/gfm/gfm.js	2022-02-09 22:50:18.145862052 +0100
@@ -97,5 +97,5 @@
         }
       }
-      if (stream.match(urlRE) &&
+      /*if (stream.match(urlRE) &&
           stream.string.slice(stream.start - 2, stream.start) != "](" &&
           (stream.start == 0 || /\W/.test(stream.string.charAt(stream.start - 1)))) {
@@ -106,5 +106,5 @@
         state.combineTokens = true;
         return "link";
-      }
+      }*/
       stream.next();
       return null;
diff -wNarU2 codemirror-5.65.1-orig/mode/meta.js codemirror-5.65.1/mode/meta.js
--- codemirror-5.65.1-orig/mode/meta.js	2022-01-20 13:06:23.000000000 +0100
+++ codemirror-5.65.1/mode/meta.js	2022-02-09 22:50:18.145862052 +0100
@@ -13,4 +13,5 @@
 
   CodeMirror.modeInfo = [
+    /*
     {name: "APL", mime: "text/apl", mode: "apl", ext: ["dyalog", "apl"]},
     {name: "PGP", mimes: ["application/pgp", "application/pgp-encrypted", "application/pgp-keys", "application/pgp-signature"], mode: "asciiarmor", ext: ["asc", "pgp", "sig"]},
@@ -56,5 +57,7 @@
     {name: "Gas", mime: "text/x-gas", mode: "gas", ext: ["s"]},
     {name: "Gherkin", mime: "text/x-feature", mode: "gherkin", ext: ["feature"]},
+    */
     {name: "GitHub Flavored Markdown", mime: "text/x-gfm", mode: "gfm", file: /^(readme|contributing|history)\.md$/i},
+    /*
     {name: "Go", mime: "text/x-go", mode: "go", ext: ["go"]},
     {name: "Groovy", mime: "text/x-groovy", mode: "groovy", ext: ["groovy", "gradle"], file: /^Jenkinsfile$/},
@@ -65,5 +68,7 @@
     {name: "HXML", mime: "text/x-hxml", mode: "haxe", ext: ["hxml"]},
     {name: "ASP.NET", mime: "application/x-aspx", mode: "htmlembedded", ext: ["aspx"], alias: ["asp", "aspx"]},
+    */
     {name: "HTML", mime: "text/html", mode: "htmlmixed", ext: ["html", "htm", "handlebars", "hbs"], alias: ["xhtml"]},
+    /*
     {name: "HTTP", mime: "message/http", mode: "http"},
     {name: "IDL", mime: "text/x-idl", mode: "idl", ext: ["pro"]},
@@ -82,5 +87,7 @@
     {name: "LiveScript", mime: "text/x-livescript", mode: "livescript", ext: ["ls"], alias: ["ls"]},
     {name: "Lua", mime: "text/x-lua", mode: "lua", ext: ["lua"]},
+    */
     {name: "Markdown", mime: "text/x-markdown", mode: "markdown", ext: ["markdown", "md", "mkd"]},
+    /*
     {name: "mIRC", mime: "text/mirc", mode: "mirc"},
     {name: "MariaDB SQL", mime: "text/x-mariadb", mode: "sql"},
@@ -163,5 +170,7 @@
     {name: "VHDL", mime: "text/x-vhdl", mode: "vhdl", ext: ["vhd", "vhdl"]},
     {name: "Vue.js Component", mimes: ["script/x-vue", "text/x-vue"], mode: "vue", ext: ["vue"]},
+    */
     {name: "XML", mimes: ["application/xml", "text/xml"], mode: "xml", ext: ["xml", "xsl", "xsd", "svg"], alias: ["rss", "wsdl", "xsd"]},
+    /*
     {name: "XQuery", mime: "application/xquery", mode: "xquery", ext: ["xy", "xquery"]},
     {name: "Yacas", mime: "text/x-yacas", mode: "yacas", ext: ["ys"]},
@@ -172,4 +181,5 @@
     {name: "msgenny", mime: "text/x-msgenny", mode: "mscgen", ext: ["msgenny"]},
     {name: "WebAssembly", mime: "text/webassembly", mode: "wast", ext: ["wat", "wast"]},
+    */
   ];
   // Ensure all modes have a mime property for backwards compatibility
diff -wNarU2 codemirror-5.65.1-orig/src/display/selection.js codemirror-5.65.1/src/display/selection.js
--- codemirror-5.65.1-orig/src/display/selection.js	2022-01-20 13:06:23.000000000 +0100
+++ codemirror-5.65.1/src/display/selection.js	2022-02-09 22:50:18.145862052 +0100
@@ -96,29 +96,21 @@
     let order = getOrder(lineObj, doc.direction)
     iterateBidiSections(order, fromArg || 0, toArg == null ? lineLen : toArg, (from, to, dir, i) => {
-      let ltr = dir == "ltr"
-      let fromPos = coords(from, ltr ? "left" : "right")
-      let toPos = coords(to - 1, ltr ? "right" : "left")
+      let fromPos = coords(from, "left")
+      let toPos = coords(to - 1, "right")
 
       let openStart = fromArg == null && from == 0, openEnd = toArg == null && to == lineLen
       let first = i == 0, last = !order || i == order.length - 1
       if (toPos.top - fromPos.top <= 3) { // Single line
-        let openLeft = (docLTR ? openStart : openEnd) && first
-        let openRight = (docLTR ? openEnd : openStart) && last
-        let left = openLeft ? leftSide : (ltr ? fromPos : toPos).left
-        let right = openRight ? rightSide : (ltr ? toPos : fromPos).right
+        let openLeft = openStart && first
+        let openRight =  openEnd && last
+        let left = openLeft ? leftSide : fromPos.left
+        let right = openRight ? rightSide : toPos.right
         add(left, fromPos.top, right - left, fromPos.bottom)
       } else { // Multiple lines
         let topLeft, topRight, botLeft, botRight
-        if (ltr) {
-          topLeft = docLTR && openStart && first ? leftSide : fromPos.left
-          topRight = docLTR ? rightSide : wrapX(from, dir, "before")
-          botLeft = docLTR ? leftSide : wrapX(to, dir, "after")
-          botRight = docLTR && openEnd && last ? rightSide : toPos.right
-        } else {
-          topLeft = !docLTR ? leftSide : wrapX(from, dir, "before")
-          topRight = !docLTR && openStart && first ? rightSide : fromPos.right
-          botLeft = !docLTR && openEnd && last ? leftSide : toPos.left
-          botRight = !docLTR ? rightSide : wrapX(to, dir, "after")
-        }
+          topLeft = openStart && first ? leftSide : fromPos.left
+          topRight = rightSide
+          botLeft = leftSide
+          botRight = openEnd && last ? rightSide : toPos.right
         add(topLeft, fromPos.top, topRight - topLeft, fromPos.bottom)
         if (fromPos.bottom < toPos.top) add(leftSide, fromPos.bottom, null, toPos.top)
diff -wNarU2 codemirror-5.65.1-orig/src/input/ContentEditableInput.js codemirror-5.65.1/src/input/ContentEditableInput.js
--- codemirror-5.65.1-orig/src/input/ContentEditableInput.js	2022-01-20 13:06:23.000000000 +0100
+++ codemirror-5.65.1/src/input/ContentEditableInput.js	2022-02-09 22:50:18.145862052 +0100
@@ -400,4 +400,5 @@
   let info = mapFromLineView(view, line, pos.line)
 
+  /*
   let order = getOrder(line, cm.doc.direction), side = "left"
   if (order) {
@@ -405,4 +406,5 @@
     side = partPos % 2 ? "right" : "left"
   }
+  */
   let result = nodeAndOffsetInLineMap(info.map, pos.ch, side)
   result.offset = result.collapse == "right" ? result.end : result.start
diff -wNarU2 codemirror-5.65.1-orig/src/input/movement.js codemirror-5.65.1/src/input/movement.js
--- codemirror-5.65.1-orig/src/input/movement.js	2022-01-20 13:06:23.000000000 +0100
+++ codemirror-5.65.1/src/input/movement.js	2022-02-09 22:50:18.145862052 +0100
@@ -15,4 +15,5 @@
 
 export function endOfLine(visually, cm, lineObj, lineNo, dir) {
+  /*
   if (visually) {
     if (cm.doc.direction == "rtl") dir = -dir
@@ -39,8 +40,11 @@
     }
   }
+  */
   return new Pos(lineNo, dir < 0 ? lineObj.text.length : 0, dir < 0 ? "before" : "after")
 }
 
 export function moveVisually(cm, line, start, dir) {
+  return moveLogically(line, start, dir)
+  /*
   let bidi = getOrder(line, cm.doc.direction)
   if (!bidi) return moveLogically(line, start, dir)
@@ -109,3 +113,4 @@
   // Case 4: Nowhere to move
   return null
+  */
 }
diff -wNarU2 codemirror-5.65.1-orig/src/line/line_data.js codemirror-5.65.1/src/line/line_data.js
--- codemirror-5.65.1-orig/src/line/line_data.js	2022-01-20 13:06:23.000000000 +0100
+++ codemirror-5.65.1/src/line/line_data.js	2022-02-09 22:54:11.542722046 +0100
@@ -3,5 +3,5 @@
 import { elt, eltP, joinClasses } from "../util/dom.js"
 import { eventMixin, signal } from "../util/event.js"
-import { hasBadBidiRects, zeroWidthElement } from "../util/feature_detection.js"
+import { zeroWidthElement } from "../util/feature_detection.js"
 import { lst, spaceStr } from "../util/misc.js"
 
@@ -79,6 +79,6 @@
     // Optionally wire in some hacks into the token-rendering
     // algorithm, to deal with browser quirks.
-    if (hasBadBidiRects(cm.display.measure) && (order = getOrder(line, cm.doc.direction)))
-      builder.addToken = buildTokenBadBidi(builder.addToken, order)
+    //if (hasBadBidiRects(cm.display.measure) && (order = getOrder(line, cm.doc.direction)))
+    //  builder.addToken = buildTokenBadBidi(builder.addToken, order)
     builder.map = []
     let allowFrontierUpdate = lineView != cm.display.externalMeasured && lineNo(line)
diff -wNarU2 codemirror-5.65.1-orig/src/measurement/position_measurement.js codemirror-5.65.1/src/measurement/position_measurement.js
--- codemirror-5.65.1-orig/src/measurement/position_measurement.js	2022-01-20 13:06:23.000000000 +0100
+++ codemirror-5.65.1/src/measurement/position_measurement.js	2022-02-09 22:50:18.145862052 +0100
@@ -382,5 +382,6 @@
     sticky = "after"
   }
-  if (!order) return get(sticky == "before" ? ch - 1 : ch, sticky == "before")
+  /*if (!order)*/ return get(sticky == "before" ? ch - 1 : ch, sticky == "before")
+  /*
 
   function getBidi(ch, partPos, invert) {
@@ -393,4 +394,5 @@
   if (other != null) val.other = getBidi(ch, other, sticky != "before")
   return val
+  */
 }
 
@@ -470,4 +472,5 @@
   let begin = 0, end = lineObj.text.length, ltr = true
 
+  /*
   let order = getOrder(lineObj, cm.doc.direction)
   // If the line isn't plain left-to-right text, first figure out
@@ -484,4 +487,5 @@
     end = ltr ? part.to : part.from - 1
   }
+  */
 
   // A binary search to find the first character whose bounding box
@@ -528,4 +532,5 @@
 }
 
+/*
 function coordsBidiPart(cm, lineObj, lineNo, preparedMeasure, order, x, y) {
   // Bidi parts are sorted left-to-right, and in a non-line-wrapping
@@ -582,4 +587,5 @@
   return part
 }
+*/
 
 let measureText
diff -wNarU2 codemirror-5.65.1-orig/src/util/bidi.js codemirror-5.65.1/src/util/bidi.js
--- codemirror-5.65.1-orig/src/util/bidi.js	2022-01-20 13:06:23.000000000 +0100
+++ codemirror-5.65.1/src/util/bidi.js	2022-02-09 22:50:18.145862052 +0100
@@ -4,5 +4,5 @@
 
 export function iterateBidiSections(order, from, to, f) {
-  if (!order) return f(from, to, "ltr", 0)
+  /*if (!order)*/ return f(from, to, "ltr", 0) /*
   let found = false
   for (let i = 0; i < order.length; ++i) {
@@ -14,4 +14,5 @@
   }
   if (!found) f(from, to, "ltr")
+*/
 }
 
@@ -32,5 +33,7 @@
     }
   }
-  return found != null ? found : bidiOther
+  var ret = found != null ? found : bidiOther
+  console.log("getBidiPartAt(%s,%s,%s) => [%s]", order, ch, sticky, ret)
+  return ret
 }
 
@@ -55,4 +58,7 @@
 // N (ON):  Other Neutrals
 
+let bidiOrdering = (function() { return function(str, direction) { return false; }})();
+/*
+
 // Returns null if characters are ordered as they appear
 // (left-to-right), or an array of sections ({from, to, level}
@@ -81,5 +87,5 @@
   }
 
-  return function(str, direction) {
+  var fun = function(str, direction) {
     let outerType = direction == "ltr" ? "L" : "R"
 
@@ -204,5 +210,11 @@
     return direction == "rtl" ? order.reverse() : order
   }
+  return function(str, direction) {
+    var ret = fun(str, direction);
+    console.log("bidiOrdering inner ([%s], %s) => [%s]", str, direction, ret);
+    return ret;
+  }
 })()
+*/
 
 // Get the bidi ordering for the given line (and cache it). Returns
@@ -210,6 +222,4 @@
 // BidiSpan objects otherwise.
 export function getOrder(line, direction) {
-  let order = line.order
-  if (order == null) order = line.order = bidiOrdering(line.text, direction)
-  return order
+  return false;
 }
diff -wNarU2 codemirror-5.65.1-orig/src/util/feature_detection.js codemirror-5.65.1/src/util/feature_detection.js
--- codemirror-5.65.1-orig/src/util/feature_detection.js	2022-01-20 13:06:23.000000000 +0100
+++ codemirror-5.65.1/src/util/feature_detection.js	2022-02-09 22:50:18.145862052 +0100
@@ -25,4 +25,5 @@
 }
 
+/*
 // Feature-detect IE's crummy client rect reporting for bidi text
 let badBidiRects
@@ -36,4 +37,5 @@
   return badBidiRects = (r1.right - r0.right < 3)
 }
+*/
 
 // See if "".split is the broken IE version, if so, provide an
