FROM    alpine:latest
WORKDIR /z
LABEL   org.opencontainers.image.url="https://github.com/9001/copyparty" \
        org.opencontainers.image.source="https://github.com/9001/copyparty/tree/hovudstraum/scripts/docker" \
        org.opencontainers.image.licenses="MIT" \
        org.opencontainers.image.title="copyparty-dj" \
        org.opencontainers.image.description="copyparty with all optional dependencies, including musical key / bpm detection"
ENV     XDG_CONFIG_HOME=/cfg

COPY    i/bin/mtag/install-deps.sh ./
COPY    i/bin/mtag/audio-bpm.py /mtag/
COPY    i/bin/mtag/audio-key.py /mtag/
RUN     apk add -U !pyc \
            tzdata wget mimalloc2 mimalloc2-insecure \
            py3-jinja2 py3-argon2-cffi py3-pyzmq py3-openssl py3-pillow \
            py3-pip py3-cffi \
            ffmpeg \
            py3-magic \
            vips-jxl vips-heif vips-poppler vips-magick \
            py3-numpy fftw libsndfile \
            vamp-sdk vamp-sdk-libs \
        && apk add -t .bd \
            bash wget gcc g++ make cmake patchelf \
            python3-dev ffmpeg-dev fftw-dev libsndfile-dev \
            py3-wheel py3-numpy-dev libffi-dev \
            vamp-sdk-dev \
        && rm -f /usr/lib/python3*/EXTERNALLY-MANAGED \
        && python3 -m pip install pyvips \
        && bash install-deps.sh \
        && apk del py3-pip .bd \
        && chmod 777 /root \
        && ln -s /root/vamp /root/.local /

COPY    i/dist/copyparty-sfx.py innvikler.sh ./
ADD     base ./base
RUN     ash innvikler.sh dj

WORKDIR /w
EXPOSE  3923
ENTRYPOINT ["python3", "-m", "copyparty", "--no-crt", "-c", "/z/initcfg"]
