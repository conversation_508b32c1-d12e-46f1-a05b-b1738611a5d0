FROM    alpine:latest
WORKDIR /z
LABEL   org.opencontainers.image.url="https://github.com/9001/copyparty" \
        org.opencontainers.image.source="https://github.com/9001/copyparty/tree/hovudstraum/scripts/docker" \
        org.opencontainers.image.licenses="MIT" \
        org.opencontainers.image.title="copyparty-iv" \
        org.opencontainers.image.description="copyparty with <PERSON>llow, FFmpeg, libvips (image/audio/video thumbnails, audio transcoding, media tags)"
ENV     XDG_CONFIG_HOME=/cfg

RUN     apk add -U !pyc \
            tzdata wget mimalloc2 mimalloc2-insecure \
            py3-jinja2 py3-argon2-cffi py3-pyzmq py3-openssl py3-pillow \
            py3-pip py3-cffi \
            ffmpeg \
            py3-magic \
            vips-jxl vips-heif vips-poppler vips-magick \
        && apk add -t .bd \
            bash wget gcc g++ make cmake patchelf \
            python3-dev py3-wheel libffi-dev \
        && rm -f /usr/lib/python3*/EXTERNALLY-MANAGED \
        && python3 -m pip install pyvips \
        && apk del py3-pip .bd

COPY    i/dist/copyparty-sfx.py innvikler.sh ./
ADD     base ./base
RUN     ash innvikler.sh iv

WORKDIR /w
EXPOSE  3923
ENTRYPOINT ["python3", "-m", "copyparty", "--no-crt", "-c", "/z/initcfg"]
