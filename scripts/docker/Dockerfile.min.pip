FROM    alpine:latest
WORKDIR /z
LABEL   org.opencontainers.image.url="https://github.com/9001/copyparty" \
        org.opencontainers.image.source="https://github.com/9001/copyparty/tree/hovudstraum/scripts/docker" \
        org.opencontainers.image.licenses="MIT" \
        org.opencontainers.image.title="copyparty-min-pip" \
        org.opencontainers.image.description="just copyparty, no thumbnails, no media tags, no audio transcoding"
ENV     PYTHONPYCACHEPREFIX=/tmp/pyc \
        XDG_CONFIG_HOME=/cfg

RUN     apk --no-cache add python3 py3-pip !pyc \
        && rm -f /usr/lib/python3*/EXTERNALLY-MANAGED \
        && python3 -m pip install copyparty \
        && apk del py3-pip \
        && rm -rf /tmp/pyc \
        && mkdir /cfg /w \
        && chmod 777 /cfg /w \
        && echo % /cfg > initcfg

WORKDIR /w
EXPOSE  3923
ENTRYPOINT ["python3", "-m", "copyparty", "--no-crt", "--no-thumb", "-c", "/z/initcfg"]
