FROM    DO_NOT_USE_THIS_DOCKER_IMAGE
# this image is an unmaintained experiment to see whether alpine was the correct choice (it was)

#FROM    ubuntu:23.04
WORKDIR /z
LABEL   org.opencontainers.image.url="https://github.com/9001/copyparty" \
        org.opencontainers.image.source="https://github.com/9001/copyparty/tree/hovudstraum/scripts/docker" \
        org.opencontainers.image.licenses="MIT" \
        org.opencontainers.image.title="copyparty-dju" \
        org.opencontainers.image.description="copyparty with all optional dependencies, including musical key / bpm detection, and higher performance than the other editions"
ENV     PYTHONPYCACHEPREFIX=/tmp/pyc \
        XDG_CONFIG_HOME=/cfg

COPY    i/bin/mtag/install-deps.sh ./
COPY    i/bin/mtag/audio-bpm.py /mtag/
COPY    i/bin/mtag/audio-key.py /mtag/

RUN     apt update \
        && DEBIAN_FRONTEND=noninteractive apt install -y --no-install-recommends \
            wget \
            python3-argon2 python3-pillow python3-pip \
            ffmpeg libvips42 vamp-plugin-sdk \
            python3-numpy libfftw3-double3 libsndfile1 \
            gcc g++ make cmake patchelf jq \
            libavcodec-dev libavdevice-dev libavfilter-dev libavformat-dev libavutil-dev \
            libfftw3-dev python3-dev libsndfile1-dev python3-pip \
            patchelf cmake \
        && rm -f /usr/lib/python3*/EXTERNALLY-MANAGED

RUN     python3 -m pip install --user pyvips \
        && bash install-deps.sh \
        && DEBIAN_FRONTEND=noninteractive apt purge -y \
            gcc g++ make cmake patchelf jq \
            libavcodec-dev libavdevice-dev libavfilter-dev libavformat-dev libavutil-dev \
            libfftw3-dev python3-dev libsndfile1-dev python3-pip \
            patchelf cmake \
        && DEBIAN_FRONTEND=noninteractive apt-get clean -y \
        && DEBIAN_FRONTEND=noninteractive apt-get autoremove -y \
        && find /usr/ -name __pycache__ | xargs rm -rf \
        && find /usr/ -type d -name tests | grep site-packages/numpy | xargs rm -rf \
        && rm -rf \
            /var/lib/apt/lists/* \
            /tmp/pyc \
            /usr/lib/python*/dist-packages/pip \
            /usr/lib/python*/dist-packages/setuptools \
            /usr/lib/*/dri \
            /usr/lib/*/mfx \
            /usr/share/doc \
            /usr/share/X11 \
            /usr/share/fonts \
            /usr/share/libmysofa \
            /usr/share/libthai \
            /usr/share/alsa \
            /usr/share/bash-completion \
        && chmod 777 /root \
        && ln -s /root/vamp /root/.local / \
        && mkdir /cfg /w \
        && chmod 777 /cfg /w \
        && echo % /cfg > initcfg

COPY    i/dist/copyparty-sfx.py ./
WORKDIR /w
EXPOSE  3923
ENTRYPOINT ["python3", "/z/copyparty-sfx.py", "--no-crt", "-c", "/z/initcfg"]

# size: 1198 MB (wowee)
# bpm/key: 516 sec
# idx-bench: 2751 MB/s

# advantage: official packages only
# advantage: ffmpeg with gme, codec2, radiance-hdr
# drawback: python packaging is a bit jank
