# this will start `/usr/bin/copyparty-sfx.py`
# in a chroot, preventing accidental access elsewhere,
# and read copyparty config from `/etc/copyparty.d/*.conf`
#
# expose additional filesystem locations to copyparty
#   by listing them between the last `cpp` and `--`
#
# `cpp cpp` = user/group to run copyparty as; can be IDs (1000 1000)
#
# unless you add -q to disable logging, you may want to remove the
#   following line to allow buffering (slightly better performance):
#   Environment=PYTHONUNBUFFERED=x

[Unit]
Description=copyparty file server

[Service]
SyslogIdentifier=prisonparty
Environment=PYTHONUNBUFFERED=x
WorkingDirectory=/var/lib/copyparty-jail
ExecReload=/bin/kill -s USR1 $MAINPID

# stop systemd-tmpfiles-clean.timer from deleting copyparty while it's running
ExecStartPre=+/bin/bash -c 'mkdir -p /run/tmpfiles.d/ && echo "x /tmp/pe-copyparty*" > /run/tmpfiles.d/copyparty.conf'

# run copyparty
ExecStart=/bin/bash /usr/bin/prisonparty /var/lib/copyparty-jail cpp cpp \
  /etc/copyparty.d \
  -- \
  /usr/bin/python3 /usr/bin/copyparty -c /etc/copyparty.d/init

[Install]
WantedBy=multi-user.target
