<!--
  NOTE: DEPRECATED; please use the javascript version instead:
  https://github.com/9001/copyparty/blob/hovudstraum/contrib/plugins/minimal-up2k.js

  ----

  save this as .epilogue.html inside a write-only folder to declutter the UI,  makes it look like
  https://user-images.githubusercontent.com/241032/118311195-dd6ca380-b4ef-11eb-86f3-75a3ff2e1332.png

  only works if you disable the prologue/epilogue sandbox with --no-sb-lg
  which should probably be combined with --no-dot-ren to prevent damage
  (`no_sb_lg` can also be set per-volume with volflags)
-->

<style>

    /* make the up2k ui REALLY minimal by hiding a bunch of stuff: */

    #ops, #tree, #path, #wfp,  /* main tabs and navigators (tree/breadcrumbs) */

    #u2conf tr:first-child>td[rowspan]:not(#u2btn_cw),  /* most of the config options */

    #srch_dz, #srch_zd,  /* the filesearch dropzone */

    #u2cards, #u2etaw  /* and the upload progress tabs */

    {display: none !important}  /* do it! */



    /* add some margins because now it's weird */
    .opview {margin-top: 2.5em}
    #op_up2k {margin-top: 6em}

    /* and embiggen the upload button */
    #u2conf #u2btn, #u2btn {padding:1.5em 0}

    /* adjust the button area a bit */
    #u2conf.w, #u2conf.ww {width: 35em !important; margin: 5em auto}

    /* a */
    #op_up2k {min-height: 0}

</style>

<a href="#" onclick="this.parentNode.innerHTML='';">show advanced options</a>
