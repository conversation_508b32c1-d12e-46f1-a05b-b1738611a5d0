# look for "max clients:" when starting copyparty, as nginx should
# not accept more consecutive clients than what copyparty is able to;
# nginx default is 512  (worker_processes 1, worker_connections 512)
#
# ======================================================================
#
# to reverse-proxy a specific path/subpath/location below a domain
# (rather than a complete subdomain), for example "/qw/er", you must
# run copyparty with --rp-loc /qw/as and also change the following:
# 	location / {
# 		proxy_pass http://cpp_tcp;
# to this:
# 	location /qw/er/ {
# 		proxy_pass http://cpp_tcp/qw/er/;
#
# ======================================================================
#
# rarely, in some extreme usecases, it can be good to add -j0
# (40'000 requests per second, or 20gbps upload/download in parallel)
# but this is usually counterproductive and slightly buggy
#
# ======================================================================
#
# on fedora/rhel, remember to setsebool -P httpd_can_network_connect 1
#
# ======================================================================
#
# if you are behind cloudflare (or another CDN/WAF/protection service),
# remember to reject all connections which are not coming from your
# protection service -- for cloudflare in particular, you can
# generate the list of permitted IP ranges like so:
#   (curl -s https://www.cloudflare.com/ips-v{4,6} | sed 's/^/allow /; s/$/;/'; echo; echo "deny all;") > /etc/nginx/cloudflare-only.conf
#
# and then enable it below by uncomenting the cloudflare-only.conf line
#
# ======================================================================


upstream cpp_tcp {
	# alternative 1: connect to copyparty using tcp;
	# cpp_uds is slightly faster and more secure, but
	# cpp_tcp is easier to setup and "just works"
	# ...you should however restrict copyparty to only
	# accept connections from nginx by adding these args:
	# -i 127.0.0.1

	server 127.0.0.1:3923 fail_timeout=1s;
	keepalive 1;
}


upstream cpp_uds {
	# alternative 2: unix-socket, aka. "unix domain socket";
	# 5-10% faster, and better isolation from other software,
	# but there must be at least one unix-group which both
	# nginx and copyparty is a member of; if that group is
	# "www" then run copyparty with the following args:
	# -i unix:770:www:/dev/shm/party.sock

	server unix:/dev/shm/party.sock fail_timeout=1s;
	keepalive 1;
}


server {
	listen 443 ssl;
	listen [::]:443 ssl;

	server_name fs.example.com;

	# uncomment the following line to reject non-cloudflare connections, ensuring client IPs cannot be spoofed:
	#include /etc/nginx/cloudflare-only.conf;

	location / {
		# recommendation: replace cpp_tcp with cpp_uds below
		proxy_pass http://cpp_tcp;
		proxy_redirect off;
		# disable buffering (next 4 lines)
		proxy_http_version 1.1;
		client_max_body_size 0;
		proxy_buffering off;
		proxy_request_buffering off;
		# improve download speed from 600 to 1500 MiB/s
		proxy_buffers 32 8k;
		proxy_buffer_size 16k;
		proxy_busy_buffers_size 24k;

		proxy_set_header   Connection        "Keep-Alive";
		proxy_set_header   Host              $host;
		proxy_set_header   X-Real-IP         $remote_addr;
		proxy_set_header   X-Forwarded-Proto $scheme;
		proxy_set_header   X-Forwarded-For   $proxy_add_x_forwarded_for;
		# NOTE: with cloudflare you want this X-Forwarded-For instead:
		#proxy_set_header   X-Forwarded-For   $http_cf_connecting_ip;
	}
}


# default client_max_body_size (1M) blocks uploads larger than 256 MiB
client_max_body_size 1024M;
client_header_timeout 610m;
client_body_timeout 610m;
send_timeout 610m;
