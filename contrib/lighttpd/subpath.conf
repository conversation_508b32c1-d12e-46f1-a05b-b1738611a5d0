# example usage for benchmarking:
#
#   taskset -c 1 lighttpd -Df ~/dev/copyparty/contrib/lighttpd/subpath.conf
#
# lighttpd can connect to copyparty using either tcp (127.0.0.1)
# or a unix-socket, but unix-sockets are 37% faster because
# lighttpd doesn't reuse tcp connections, so we're doing unix-sockets
#
# this means we must run copyparty with one of the following:
#
#   -i unix:777:/dev/shm/party.sock
#   -i unix:777:/dev/shm/party.sock,127.0.0.1
#
# also since this example proxies a subpath instead of the
# recommended subdomain-proxying, we must also specify this:
#
#   --rp-loc files
#
# on fedora/rhel, remember to setsebool -P httpd_can_network_connect 1

server.port = 80
server.document-root = "/var/empty"
server.upload-dirs = ( "/dev/shm", "/tmp" )
server.modules = ( "mod_proxy" )
$HTTP["url"] =~ "^/files" {
    proxy.forwarded = ( "for" => 1, "proto" => 1 )
    proxy.server = ( "" => ( ( "host" => "/dev/shm/party.sock" ) ) )

    # if you really need to use tcp instead of unix-sockets, do this instead:
    #proxy.server = ( "" => ( ( "host" => "127.0.0.1", "port" => "3923" ) ) )
}
