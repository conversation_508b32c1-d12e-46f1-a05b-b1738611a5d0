# this will start `/usr/local/bin/copyparty-sfx.py` and
# read copyparty config from `/etc/copyparty.conf`, for example:
# https://github.com/9001/copyparty/blob/hovudstraum/contrib/systemd/copyparty.conf
#
# installation:
#   wget https://github.com/9001/copyparty/releases/latest/download/copyparty-sfx.py -O /usr/local/bin/copyparty-sfx.py
#   useradd -r -s /sbin/nologin -m -d /var/lib/copyparty copyparty
#   firewall-cmd --permanent --add-port=3923/tcp  # --zone=libvirt
#   firewall-cmd --reload
#   cp -pv copyparty.service /etc/systemd/system/
#   cp -pv copyparty.conf /etc/
#   restorecon -vr /etc/systemd/system/copyparty.service  # on fedora/rhel
#   systemctl daemon-reload && systemctl enable --now copyparty
#
# every time you edit this file, you must "systemctl daemon-reload"
# for the changes to take effect and then "systemctl restart copyparty"
#
# if it fails to start, first check this: systemctl status copyparty
# then try starting it while viewing logs:
#   journalctl -fan 100
#   tail -Fn 100 /var/log/copyparty/$(date +%Y-%m%d.log)
#
# if you run into any issues, for example thumbnails not working,
# try removing the "some quick hardening" section and then please
# let me know if that actually helped so we can look into it
#
# you may want to:
#  - change "User=copyparty" and "/var/lib/copyparty/" to another user
#  - edit /etc/copyparty.conf to configure copyparty
# and in the ExecStart= line:
#  - change '/usr/bin/python3' to another interpreter
#
# with `Type=notify`, copyparty will signal systemd when it is ready to
#   accept connections; correctly delaying units depending on copyparty.
#   But note that journalctl will get the timestamps wrong due to
#   python disabling line-buffering, so messages are out-of-order:
#   https://user-images.githubusercontent.com/241032/126040249-cb535cc7-c599-4931-a796-a5d9af691bad.png
#
########################################################################
########################################################################


[Unit]
Description=copyparty file server

[Service]
Type=notify
SyslogIdentifier=copyparty
Environment=PYTHONUNBUFFERED=x
ExecReload=/bin/kill -s USR1 $MAINPID
PermissionsStartOnly=true

## user to run as + where the TLS certificate is (if any)
##
User=copyparty
Group=copyparty
WorkingDirectory=/var/lib/copyparty
Environment=XDG_CONFIG_HOME=/var/lib/copyparty/.config

## OPTIONAL: allow copyparty to listen on low ports (like 80/443);
##   you need to uncomment the "p: 80,443,3923" in the config too
##   ------------------------------------------------------------
##   a slightly safer alternative is to enable partyalone.service
##   which does portforwarding with nftables instead, but an even
##   better option is to use a reverse-proxy (nginx/caddy/...)
##
AmbientCapabilities=CAP_NET_BIND_SERVICE

## some quick hardening; TODO port more from the nixos package
##
MemoryMax=50%
MemorySwapMax=50%
ProtectClock=true
ProtectControlGroups=true
ProtectHostname=true
ProtectKernelLogs=true
ProtectKernelModules=true
ProtectKernelTunables=true
ProtectProc=invisible
RemoveIPC=true
RestrictNamespaces=true
RestrictRealtime=true
RestrictSUIDSGID=true

## create a directory for logfiles;
##   this defines $LOGS_DIRECTORY which is used in copyparty.conf
##
LogsDirectory=copyparty

## finally, start copyparty and give it the config file:
##
ExecStart=/usr/bin/python3 /usr/local/bin/copyparty-sfx.py -c /etc/copyparty.conf

# NOTE: if you installed copyparty from an OS package repo (nice)
#   then you probably want something like this instead:
#ExecStart=/usr/bin/copyparty -c /etc/copyparty.conf

[Install]
WantedBy=multi-user.target
