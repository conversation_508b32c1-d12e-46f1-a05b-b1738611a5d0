# not actually YAM<PERSON> but lets pretend:
# -*- mode: yaml -*-
# vim: ft=yaml:


# put this file in /etc/


[global]
  e2dsa  # enable file indexing and filesystem scanning
  e2ts   # and enable multimedia indexing
  ansi   # and colors in log messages

  # disable logging to stdout/journalctl and log to a file instead;
  # $LOGS_DIRECTORY is usually /var/log/copyparty (comes from systemd)
  # and copyparty replaces %Y-%m%d with Year-MonthDay, so the
  # full path will be something like /var/log/copyparty/2023-1130.txt
  # (note: enable compression by adding .xz at the end)
  q, lo: $LOGS_DIRECTORY/%Y-%m%d.log

  # p: 80,443,3923   # listen on 80/443 as well (requires CAP_NET_BIND_SERVICE)
  # i: 127.0.0.1     # only allow connections from localhost (reverse-proxies)
  # ftp: 3921        # enable ftp server on port 3921
  # p: 3939          # listen on another port
  # df: 16           # stop accepting uploads if less than 16 GB free disk space
  # ver              # show copyparty version in the controlpanel
  # grid             # show thumbnails/grid-view by default
  # theme: 2         # monokai
  # name: datasaver  # change the server-name that's displayed in the browser
  # stats, nos-dup   # enable the prometheus endpoint, but disable the dupes counter (too slow)
  # no-robots, force-js  # make it harder for search engines to read your server


[accounts]
  ed: wark  # username: password


[/]            # create a volume at "/" (the webroot), which will
  /mnt         # share the contents of the "/mnt" folder
  accs:
    rw: *      # everyone gets read-write access, but
    rwmda: ed  # the user "ed" gets read-write-move-delete-admin
