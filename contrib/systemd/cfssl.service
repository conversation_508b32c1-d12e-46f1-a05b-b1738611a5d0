# NOTE: this is now a built-in feature in copyparty
# but you may still want this if you have specific needs
#
# systemd service which generates a new TLS certificate on each boot,
# that way the one-year expiry time won't cause any issues --
# just have everyone trust the ca.pem once every 10 years
#
# assumptions/placeholder values:
#  * this script and copyparty runs as user "cpp"
#  * copyparty repo is at ~cpp/dev/copyparty
#  * CA is named partylan
#  * server IPs = ******** and *************
#  * server hostname = party.lan

[Unit]
Description=copyparty certificate generator
Before=copyparty.service

[Service]
User=cpp
Type=oneshot
SyslogIdentifier=cpp-cert
ExecStart=/bin/bash -c 'cd ~/dev/copyparty/contrib && ./cfssl.sh partylan ********,*************,party.lan y'

[Install]
WantedBy=multi-user.target
