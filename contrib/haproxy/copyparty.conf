# this config is essentially two separate examples;
#
#   foo1 connects to copyparty using tcp, and
#   foo2 uses unix-sockets for 27% higher performance
#
# to use foo2 you must run copyparty with one of the following:
#
#   -i unix:777:/dev/shm/party.sock
#   -i unix:777:/dev/shm/party.sock,127.0.0.1

defaults
  mode http
  option forwardfor
  timeout connect 1s
  timeout client 610s
  timeout server 610s

listen foo1
  bind *:8081
  server srv1 127.0.0.1:3923 maxconn 512

listen foo2
  bind *:8082
  server srv1 /dev/shm/party.sock maxconn 512
