{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Run copyparty",
            "type": "python",
            "request": "launch",
            "module": "copyparty",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "justMyCode": false,
            "env": {
                "PYDEVD_DISABLE_FILE_VALIDATION": "1",
                "PYTHONWARNINGS": "always", //error
            },
            "args": [
                //"-nw",
                "-ed",
                "-emp",
                "-e2dsa",
                "-e2ts",
                "-mtp=.bpm=f,bin/mtag/audio-bpm.py",
                "-aed:wark",
                "-vsrv::r:rw,ed:c,dupe",
                "-vdist:dist:r"
            ]
        },
        {
            "name": "No debug",
            "preLaunchTask": "no_dbg",
            "type": "python",
            //"request": "attach", "port": 42069
            // fork: nc -l 42069 </dev/null
        },
        {
            "name": "Run active unit test",
            "type": "python",
            "request": "launch",
            "module": "unittest",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "args": [
                "-v",
                "${file}"
            ]
        },
        {
            "name": "Python: Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": false
        },
    ]
}