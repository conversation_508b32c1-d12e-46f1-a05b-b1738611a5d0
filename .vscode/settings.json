{
    "workbench.colorCustomizations": {
        // https://ocv.me/dot/bifrost.html
        "terminal.background": "#1e1e1e",
        "terminal.foreground": "#d2d2d2",
        "terminalCursor.background": "#93A1A1",
        "terminalCursor.foreground": "#93A1A1",
        "terminal.ansiBlack": "#404040",
        "terminal.ansiRed": "#f03669",
        "terminal.ansiGreen": "#b8e346",
        "terminal.ansiYellow": "#ffa402",
        "terminal.ansiBlue": "#02a2ff",
        "terminal.ansiMagenta": "#f65be3",
        "terminal.ansiCyan": "#3da698",
        "terminal.ansiWhite": "#d2d2d2",
        "terminal.ansiBrightBlack": "#606060",
        "terminal.ansiBrightRed": "#c75b79",
        "terminal.ansiBrightGreen": "#c8e37e",
        "terminal.ansiBrightYellow": "#ffbe4a",
        "terminal.ansiBrightBlue": "#71cbff",
        "terminal.ansiBrightMagenta": "#b67fe3",
        "terminal.ansiBrightCyan": "#9cf0ed",
        "terminal.ansiBrightWhite": "#ffffff",
    },
    "python.terminal.activateEnvironment": false,
    "python.analysis.enablePytestSupport": false,
    "python.analysis.typeCheckingMode": "standard",
    "python.testing.pytestEnabled": false,
    "python.testing.unittestEnabled": true,
    "python.testing.unittestArgs": [
        "-v",
        "-s",
        "./tests",
        "-p",
        "test_*.py"
    ],
    // python3 -m isort --py=27 --profile=black ~/dev/copyparty/{copyparty,tests}/*.py && python3 -m black -t py27 ~/dev/copyparty/{copyparty,tests,bin}/*.py $(find ~/dev/copyparty/copyparty/stolen -iname '*.py')
    "editor.formatOnSave": false,
    "[html]": {
        "editor.formatOnSave": false,
        "editor.autoIndent": "keep",
    },
    "[css]": {
        "editor.formatOnSave": false,
    },
    "files.associations": {
        "*.makefile": "makefile"
    },
}