RTL support is not planned, but it would be
something like this (just a whole lot more)

diff --git a/copyparty/web/browser.css b/copyparty/web/browser.css
index e66279d4..2888be56 100644
--- a/copyparty/web/browser.css
+++ b/copyparty/web/browser.css
@@ -653,12 +653,10 @@ a:hover {
 .s0:after,
 .s1:after {
 	content: '⌄';
-	margin-left: -.15em;
 }
 .s0r:after,
 .s1r:after {
 	content: '⌃';
-	margin-left: -.15em;
 }
 .s0:after,
 .s0r:after {
@@ -668,6 +666,19 @@ a:hover {
 .s1r:after {
 	color: var(--sort-2);
 }
+.ltr .s0:after,
+.ltr .s1:after,
+.ltr .s0r:after,
+.ltr .s1r:after {
+	margin-left: -.15em;
+}
+.rtl .s0:after,
+.rtl .s1:after,
+.rtl .s0r:after,
+.rtl .s1r:after {
+	margin-left: -.5em;
+	padding: 0 .25em 0 0;
+}
 #files thead th:after {
 	margin-right: -.5em;
 }
diff --git a/copyparty/web/browser.js b/copyparty/web/browser.js
index 33965a70..bf425cc7 100644
--- a/copyparty/web/browser.js
+++ b/copyparty/web/browser.js
@@ -1797,9 +1797,13 @@ var Ls = {
 
 		"lang_set": "刷新以使更改生效？",
 	},
+	"foo": {
+		"tt": "Foobar",
+		"rtl": "rtl",
+	},
 };
 
-var LANGS = ["eng", "nor", "chi"];
+var LANGS = ["eng", "nor", "chi", "foo"];
 
 if (window.langmod)
 	langmod();
@@ -1819,7 +1823,7 @@ for (var a = 0; a < LANGS.length; a++) {
 			t2 = Ls[LANGS[i2]];
 
 		for (var k in t1)
-			if (!t2[k]) {
+			if (!t2[k] && k != 'rtl') {
 				console.log("E missing TL", LANGS[i2], k);
 				t2[k] = t1[k];
 			}
@@ -1829,6 +1833,10 @@ for (var a = 0; a < LANGS.length; a++) {
 if (!has(LANGS, lang))
 	alert('unsupported --lang "' + lang + '" specified in server args;\nplease use one of these: ' + LANGS);
 
+if (L.rtl)
+	document.documentElement.setAttribute('dir', L.rtl);
+document.documentElement.className = L.rtl || 'ltr';
+
 modal.load();
 
 
