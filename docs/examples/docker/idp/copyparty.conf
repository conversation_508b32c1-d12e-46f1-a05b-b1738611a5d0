# not actually <PERSON><PERSON><PERSON> but lets pretend:
# -*- mode: yaml -*-
# vim: ft=yaml:


# example config for how copyparty can be used with an identity
# provider, replacing the built-in authentication/authorization
# mechanism, and instead expecting the reverse-proxy to provide
# the requester's username (and possibly a group-name, for
# optional group-based access control)
#
# the filesystem-path `/w` is used as the storage location
# because that is the data-volume in the docker containers,
# because a deployment like this (with an IdP) is more commonly
# seen in containerized environments -- but this is not required
#
# the example group "su" (super-user) is the admins group


[global]
  e2dsa  # enable file indexing and filesystem scanning
  e2ts   # enable multimedia indexing
  ansi   # enable colors in log messages

  # enable IdP support by expecting username/groupname in
  # http-headers provided by the reverse-proxy; header "X-IdP-User"
  # will contain the username, "X-IdP-Group" the groupname
  idp-h-usr: x-idp-user
  idp-h-grp: x-idp-group

  # but copyparty will refuse to accept those headers unless you
  # tell it the LAN IP of the reverse-proxy to expect them from,
  # preventing malicious users from pretending to be the proxy;
  # pay attention to the warning message in the logs and then
  # adjust the following config option accordingly:
  xff-src: ***********/16

  # or just allow all LAN / private IPs (probably good enough):
  xff-src: lan

  # an additional, optional security measure is to expect a
  # secret header name from the reverse-proxy; you can enable
  # this feature by setting the header-name to expect here:
  #idp-h-key: shangala-bangala

  # convenient debug option:
  # log all incoming request headers from the proxy
  #ihead: *

[/]      # create a volume at "/" (the webroot), which will
  /w     # share /w (the docker data volume)
  accs:
    rw: *       # everyone gets read-access, but
    rwmda: @su  # the group "su" gets read-write-move-delete-admin


[/u/${u}]    # each user gets their own home-folder at /u/username
  /w/u/${u}  # which will be "u/username" in the docker data volume
  accs:
    r: *              # read-access for anyone, and
    rwmda: ${u}, @su  # read-write-move-delete-admin for that username + the "su" group


[/u/${u}/priv]    # each user also gets a private area at /u/username/priv
  /w/u/${u}/priv  # stored at DATAVOLUME/u/username/priv
  accs:
    rwmda: ${u}, @su  # read-write-move-delete-admin for that username + the "su" group


[/lounge/${g}]    # each group gets their own shared volume
  /w/lounge/${g}  # stored at DATAVOLUME/lounge/groupname
  accs:
    r: *               # read-access for anyone, and
    rwmda: @${g}, @su  # read-write-move-delete-admin for that group + the "su" group


[/lounge/${g}/priv]    # and a private area for each group too
  /w/lounge/${g}/priv  # stored at DATAVOLUME/lounge/groupname/priv
  accs:
    rwmda: @${g}, @su  # read-write-move-delete-admin for that group + the "su" group


[/sus/${u%+su}]  # users which ARE members of group "su" gets /sus/username
  /w/tank1/${u}  # which will be "tank1/username" in the docker data volume
  accs:
    rwmda: ${u}  # read-write-move-delete-admin for that username


[/m8s/${u%-su}]  # users which are NOT members of group "su" gets /m8s/username
  /w/tank2/${u}  # which will be "tank2/username" in the docker data volume
  accs:
    rwmda: ${u}  # read-write-move-delete-admin for that username


# and create some strategic volumes to prevent anyone from gaining
# unintended access to priv folders if the users/groups db is lost
[/u]
  /w/u
  accs:
    rwmda: @su
[/lounge]
  /w/lounge
  accs:
    rwmda: @su
[/sus]
  /w/tank1
[/m8s]
  /w/tank2


# some other things you can do:
#  [/demo/${u%-su,%-fds}]   # users which are NOT members of "su" or "fds"
#  [/demo/${u%+su,%+fds}]   # users which ARE members of BOTH "su" and "fds"
#  [/demo/${g%-su}]         # all groups except su
#  [/demo/${g%-su,%-fds}]   # all groups except su and fds
