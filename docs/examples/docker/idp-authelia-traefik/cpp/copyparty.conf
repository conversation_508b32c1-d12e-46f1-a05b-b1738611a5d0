# not actually YAM<PERSON> but lets pretend:
# -*- mode: yaml -*-
# vim: ft=yaml:


# example config for how authelia can be used to replace
# copyparty's built-in authentication/authorization mechanism,
# providing copyparty with HTTP headers through traefik to
# signify who the user is, and what groups they belong to
#
# the filesystem-path that will be shared with copyparty is
# specified in the docker-compose in the parent folder, where
# a real filesystem-path is mapped onto this container's path `/w`,
# meaning `/w` in this config-file is actually `/srv/pub` in the
# outside world (assuming you didn't modify that value)


[global]
  e2dsa  # enable file indexing and filesystem scanning
  e2ts   # enable multimedia indexing
  ansi   # enable colors in log messages
  #q      # disable logging for more performance

  # if we are confident that we got the docker-network config correct
  # (meaning copyparty is only accessible through traefik, and
  #  traefik makes sure that all requests go through authelia),
  # then accept X-Forwarded-For and IdP headers from any private IP:
  xff-src: lan

  # enable IdP support by expecting username/groupname in
  # http-headers provided by the reverse-proxy; header "X-IdP-User"
  # will contain the username, "X-IdP-Group" the groupname
  idp-h-usr: remote-user
  idp-h-grp: remote-groups

  # DEBUG: show all incoming request headers from traefik/authelia
  #ihead: *


[/]      # create a volume at "/" (the webroot), which will
  /w     # share /w (the docker data volume, which is mapped to /srv/pub on the host in docker-compose.yml)
  accs:
    rw: *       # everyone gets read-access, but
    rwmda: @su  # the group "su" gets read-write-move-delete-admin


[/u/${u}]    # each user gets their own home-folder at /u/username
  /w/u/${u}  # which will be "u/username" in the docker data volume
  accs:
    r: *              # read-access for anyone, and
    rwmda: ${u}, @su  # read-write-move-delete-admin for that username + the "su" group


[/u/${u}/priv]    # each user also gets a private area at /u/username/priv
  /w/u/${u}/priv  # stored at DATAVOLUME/u/username/priv
  accs:
    rwmda: ${u}, @su  # read-write-move-delete-admin for that username + the "su" group


[/lounge/${g}]    # each group gets their own shared volume
  /w/lounge/${g}  # stored at DATAVOLUME/lounge/groupname
  accs:
    r: *               # read-access for anyone, and
    rwmda: @${g}, @su  # read-write-move-delete-admin for that group + the "su" group


[/lounge/${g}/priv]    # and a private area for each group too
  /w/lounge/${g}/priv  # stored at DATAVOLUME/lounge/groupname/priv
  accs:
    rwmda: @${g}, @su  # read-write-move-delete-admin for that group + the "su" group


# and create some strategic volumes to prevent anyone from gaining
# unintended access to priv folders if the users/groups db is lost
[/u]
  /w/u
  accs:
    rwmda: @su
[/lounge]
  /w/lounge
  accs:
    rwmda: @su
