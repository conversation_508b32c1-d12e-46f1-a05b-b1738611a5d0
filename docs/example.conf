# not actually <PERSON><PERSON><PERSON> but lets pretend:
# -*- mode: yaml -*-
# vim: ft=yaml:

# append some arguments to the commandline;
# accepts anything listed in --help (leading dashes are optional)
# and inline comments are OK if there is 2 spaces before the '#'
[global]
  p: 8086, 3939  # listen on ports 8086 and 3939
  e2dsa  # enable file indexing and filesystem scanning
  e2ts   # and enable multimedia indexing
  z, qr  # and zeroconf and qrcode (you can comma-separate arguments)

# create users:
[accounts]
  ed: 123   # username: password
  k: k

# create volumes:
[/]         # create a volume at "/" (the webroot), which will
  .         # share the contents of "." (the current directory)
  accs:
    r: *    # everyone gets read-access, but
    rw: ed  # the user "ed" gets read-write

# let's specify different permissions for the "priv" subfolder
# by creating another volume at that location:
[/priv]
  ./priv
  accs:
    r: k    # the user "k" can see the contents,
    rw: ed  # while "ed" gets read-write

# share /home/<USER>/Music/ as /music and let anyone read it
# (this will replace any folder called "music" in the webroot)
[/music]
  /home/<USER>/Music
  accs:
    r: *

# and a folder where anyone can upload, but nobody can see the contents
[/dump]
  /home/<USER>/inc
  accs:
    w: *
  flags:
    e2d     # the e2d volflag enables the uploads database
    nodupe  # the nodupe volflag rejects duplicate uploads
    # (see --help-flags for all available volflags to use)

# and a folder where anyone can upload
# and anyone can access their own uploads, but nothing else
[/sharex]
  /home/<USER>/inc/sharex
  accs:
    wG: *        # wG = write-upget = see your own uploads only
    rwmd: ed, k  # read-write-modify-delete for users "ed" and "k"
  flags:
    e2d, d2t, fk: 4
    # volflag "e2d" enables the uploads database,
    # "d2t" disables multimedia parsers (in case the uploads are malicious),
    # "dthumb" disables thumbnails (same reason),
    # "fk" enables filekeys (necessary for upget permission) (4 chars long)
    # -- note that its fine to combine all the volflags on
    #    one line because only the last volflag has an argument

# this entire config file can be replaced with these arguments:
# -u ed:123 -u k:k -v .::r:a,ed -v priv:priv:r,k:rw,ed -v /home/<USER>/Music:music:r -v /home/<USER>/inc:dump:w:c,e2d,nodupe -v /home/<USER>/inc/sharex:sharex:wG:c,e2d,d2t,fk=4
# but note that the config file always wins in case of conflicts
