﻿# recipe for building an exe with nuitka (extreme jank edition)

NOTE: copyparty runs SLOWER when compiled with nuitka;
  just use copyparty-sfx.py and/or pyinstaller instead

  ( the sfx and the pyinstaller EXEs are equally fast if you
  have the latest jinja2 installed, but the older jinja that
  comes bundled with the sfx is slightly faster yet )

  roughly, copyparty-sfx.py is 6% faster than copyparty.exe
  (win10-pyinstaller), and copyparty.exe is 10% faster than
  nuitka, making copyparty-sfx.py 17% faster than nuitka

NOTE: every time a nuitka-compiled copyparty.exe is launched,
  it will show the windows firewall prompt since nuitka will
  pick a new unique location in %TEMP% to unpack an exe into,
  unlike pyinstaller which doesn't fork itself on startup...
  might be fixable by configuring nuitka differently, idk

NOTE: nuitka EXEs are larger than pyinstaller ones;
  a minimal nuitka build of just the sfx (with its bundled
  dependencies) was already the same size as the pyinstaller
  copyparty.exe which also includes Mutagen and Pillow

NOTE: nuitka takes a lot longer to build than pyinstaller
  (due to actual compilation of course, but still)

NOTE: binaries built with nuitka cannot run on windows7,
  even when compiled with python 3.6 on windows 7 itself

NOTE: `--python-flags=-m` is the magic sauce to
  correctly compile `from .util import Daemon`
  (which otherwise only explodes at runtime)

NOTE: `--deployment` doesn't seem to affect performance

########################################################################
# copypaste the rest of this file into cmd



python -m pip install --user -U nuitka

cd %homedrive%
cd %homepath%\downloads

rd /s /q copypuitka
mkdir copypuitka
cd copypuitka

rd /s /q %temp%\pe-copyparty
python ..\copyparty-sfx.py --version

move %temp%\pe-copyparty\copyparty .\
move %temp%\pe-copyparty\partftpy .\
move %temp%\pe-copyparty\ftp\pyftpdlib .\
move %temp%\pe-copyparty\j2\jinja2 .\
move %temp%\pe-copyparty\j2\markupsafe .\

rd /s /q %temp%\pe-copyparty

python -m nuitka ^
  --onefile --deployment --python-flag=-m ^
  --include-package=markupsafe ^
  --include-package=jinja2 ^
  --include-package=partftpy ^
  --include-package=pyftpdlib ^
  --include-data-dir=copyparty\web=copyparty\web ^
  --include-data-dir=copyparty\res=copyparty\res ^
  --run copyparty

