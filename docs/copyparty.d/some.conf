# not actually YAM<PERSON> but lets pretend:
# -*- mode: yaml -*-
# vim: ft=yaml:

# lets make two volumes with the same accounts/permissions for both;
# first declare the accounts just once:
[accounts]
  usr1: passw0rd
  usr2: letmein

[global]
  i: 127.0.0.1  # listen on 127.0.0.1 only,
  p: 2434       # port 2434
  e2ds  # enable file indexing+scanning
  e2ts  # and multimedia indexing+scanning
  # (inline comments are OK if there is 2 spaces before the #)

# share /usr/share/games from the server filesystem
[/vidya]
  /usr/share/games
  % foo/another.conf  # include config file with volume permissions

# and share your ~/Music folder too
[/bangers]
  ~/Music
  % foo/another.conf

# which should result in each of the volumes getting the following permissions:
# usr1 read/write/move
# usr2 read/move
#
# because another.conf sets the read/write permissions before it
# includes sibling.conf which adds the move permission
