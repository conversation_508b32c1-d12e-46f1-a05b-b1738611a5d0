# running copyparty on synology dsm nas

![synology-dsm-container-status.png](https://ocv.me/copyparty/doc/pics/dsm.png)

this has been tested on a `Synology ds218+` NAS with 1 SHR storage-pool and 1 volume, but the same steps should work in more advanced setups too

verified on DSM 7.1 and 7.2, but not on 6.x since my flea-market ds218+ refuses to install it for some reason



# ok let's go

go to controlpanel -> shared-folders, and create the following shared-folders if you don't already have appropriate ones:

* a shared-folder for configuration files, preferably on SSD if you have one

* one or more shared-folders for your actual data/media to share

(btw, when you create the shared-folders, it asks whether you want to enable data checksum and file compression, i would recommend both)

the rest of this doc assumes that these two shared-folders are named `configs` and `media1`, and that you made an empty folder inside the `configs` shared-folder named `cpp`

* your copyparty config file (see below) should be named `something.conf` directly inside that cpp folder, for example `/configs/cpp/copyparty.conf`

* during first start, copyparty will create a folder there named `copyparty`, in other words `/configs/cpp/copyparty` which you should leave alone; that's where copyparty stores its indexes and other runtime config



## recommended copyparty config

open the Package Center and install `Text Editor` (by Synology Inc.) to create and edit your copyparty config:

![synology-text-editor-copyparty-conf.png](https://ocv.me/copyparty/doc/pics/dsm-cfg.png)

* note the `copyparty` and `hist` folders in that screenshot which are autogenerated by copyparty and to be left alone

```yaml
[global]
  e2d, e2t         # remember uploads & read media tags
  rss, daw, ver    # some other nice-to-have features
  #dedup            # you may want this, or maybe not
  hist: /cfg/hist  # don't pollute the shared-folder
  name: synology   # shows in the browser, can be anything

[accounts]
  ed: wark   # username ed, password wark

[/]          # share the following at the webroot:
  /w         # the "/w" docker-volume (the shared-folder)
  accs:
    A: ed    # give Admin to username ed

# hide the synology system files by creating a hidden volume
[/@eaDir]
  /w/@eaDir
```

if you ever change the copyparty config file, then [restart the container](https://ocv.me/copyparty/doc/pics/dsm71-02.png) to make the changes take effect

okay now continue with one of these:

* [DSM v7.2 or newer](#dsm-v72-or-newer)

* [all older DSM versions](#dsm-v6x-dsm-v71x-or-older)



# DSM v7.2 or newer

`Docker` was replaced by `Container Manager` in DSM v7.2 but they're almost the same thing;

* open the `Package Center` and install the [Container Manager package](https://ocv.me/copyparty/doc/pics/dsm72-01.png) by `Docker Inc.`
* open the `Container Manager` app
* go to the `Registry` tab and search for `copyparty`
* [doubleclick copyparty/ac](https://ocv.me/copyparty/doc/pics/dsm72-02.png) and keep the [default `latest`](https://ocv.me/copyparty/doc/pics/dsm72-03.png) when it asks you which tag to use
* switch to the `Container` tab and click `Create`
* [choose `copyparty/ac:latest`](https://ocv.me/copyparty/doc/pics/dsm72-04.png) and click `Next`

finally, in the [Advanced Settings](https://ocv.me/copyparty/doc/pics/dsm72-05.png) window,

* under `Port Settings`, type `3923` into the `Local Port` textbox
* click `Add Folder` and select `/configs/cpp` on your nas (the `cpp` folder in the `configs` shared-folder), and change `Mount path` to `/cfg`
* click `Add Folder` and select `/media1` on your nas (the shared-folder that copyparty can share in its web-UI) and change `Mount path` to `/w`
  * if you are adding multiple shared-folders for media, then the `Mount path` of the 2nd folder should be something like `/w/share2` or `/w/music`

copyparty will launch and become available at http://***********:3923/ (assuming `***********` is your nas ip)


# DSM v6.x, DSM v7.1.x or older

if you're using DSM 7.1 or older, then you don't have [Container Manager](https://www.synology.com/en-global/dsm/packages/ContainerManager) yet and you'll have to use [Docker](https://www.synology.com/en-global/dsm/packages/Docker?os_ver=6.2&search=docker) instead. Here's how:

* open the `Package Center` and install the [Docker package](https://ocv.me/copyparty/doc/pics/dsm71-01.png) by `Docker Inc.`
* open the `Docker` app
* go to the `Registry` tab and search for `copyparty`
* [doubleclick copyparty/ac](https://ocv.me/copyparty/doc/pics/dsm71-02.png) and keep the [default `latest`](https://ocv.me/copyparty/doc/pics/dsm71-03.png) when it asks you which tag to use
* switch to the `Container` tab and click `Create`
* [choose `copyparty/ac:latest`](https://ocv.me/copyparty/doc/pics/dsm71-04.png) and `Next`
* in the [Network](https://ocv.me/copyparty/doc/pics/dsm71-05.png) window, keep the default `Use the selected networks: [x] bridge`
* in the [General Settings](https://ocv.me/copyparty/doc/pics/dsm71-06.png) window, just keep everything default (in other words, everything disabled)
* in the [Port Settings](https://ocv.me/copyparty/doc/pics/dsm71-07.png) window, change `Local Port` to `3923` (or choose something else, but it cannot be the default `Auto`)

finally, in the [Volume Settings](https://ocv.me/copyparty/doc/pics/dsm71-08.png) window, add a docker volume for copyparty config, and at least one volume for media-files which copyparty can share in its web-UI

* click `Add Folder` and select `/configs/cpp` on your nas (the `cpp` folder in the `configs` shared-folder), and change `Mount path` to `/cfg`
* click `Add Folder` and select `/media1` on your nas (the shared-folder that copyparty can share in its web-UI) and change `Mount path` to `/w`
* if you are adding multiple shared-folders for media, then the `Mount path` of the 2nd folder should be something like `/w/share2` or `/w/music`

copyparty will launch and become available at http://***********:3923/ (assuming `***********` is your nas ip)


# misc notes

note that if you only want to share some folders inside your data volume, and not all of it, then you can either give copyparty the whole shared-folder anyways and control/restrict access in the copyparty config file (recommended), or you can add each folder as a new docker volume (not as flexible)



## updating

to update to a new copyparty version: `Container Manager` » `Images` » `Update available` » `Update`

* DSM checks for updates every 12h; you can force a check with `sudo /var/packages/ContainerManager/target/tool/image_upgradable_checker`

* there is no auto-update feature, and beware that watchtower does not support DSM



## regarding ram usage

the ram usage indicator in both `Docker` and `Container Manager` is misleading  because it also counts the kernel disk cache which makes the number insanely high -- the synology resource monitor shows the correct values, usually less than 100 MiB

to see the actual memory usage by copyparty, see `Resource Monitor` -> `Task Manager` -> `Processes` and look at the `Private Memory` of `python3` which is probably copyparty



## regarding performance

when uploading files to the synology nas with the respective web-UIs,

* `File Station` does about 16 MiB/s,

* `Synology Drive Server` does about 50 MiB/s; deceivingly fast upload speeds at first, but when the file is fully uploaded, there is a lengthy "processing" step at the end, reducing the average speed to about 50% of the initial

* copyparty maxes the HDD write-speeds, 99 MiB/s

when uploading to the synology nas over webdav,

* `WebDAV Server` by `Synology Inc.` in the Package Center does 86 MiB/s

* copyparty does 79 MiB/s; the NAS CPU is a bottleneck because copyparty verifies the upload checksum while `WebDAV Server` doesn't
