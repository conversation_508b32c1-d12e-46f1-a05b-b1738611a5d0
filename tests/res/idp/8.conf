# -*- mode: yaml -*-
# vim: ft=yaml:

[groups]
  ga: iua, iuab, iuabc
  gb: iuab, iuabc, iub, iubc
  gc: iuabc, iubc, iuc

[/u/${u}]
  /u/${u}
  accs:
    r: *

[/uya/${u%+ga}]
  /uya/${u}
  accs:
    r: *

[/uyab/${u%+ga,%+gb}]
  /uyab/${u}
  accs:
    r: *

[/una/${u%-ga}]
  /una/${u}
  accs:
    r: *

[/unab/${u%-ga,%-gb}]
  /unab/${u}
  accs:
    r: *

[/gya/${g%+ga}]
  /gya/${g}
  accs:
    r: *

[/gna/${g%-ga}]
  /gna/${g}
  accs:
    r: *

[/gnab/${g%-ga,%-gb}]
  /gnab/${g}
  accs:
    r: *
